import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class NotificationServices with WidgetsBindingObserver {
  static var notification = FlutterLocalNotificationsPlugin();
  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
  var initializationSettingsAndroid = const AndroidInitializationSettings('@mipmap/ic_launcher');
  var initializationSettingsDarwin = const DarwinInitializationSettings(
    requestAlertPermission: true,
    requestBadgePermission: true,
    requestSoundPermission: true,
    //    onDidReceiveLocalNotification: onDidReceiveLocalNotification
  );

  void initialiseNotification() async {
    var initializationSettings = InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: DarwinInitializationSettings(
            requestAlertPermission: true,
            requestBadgePermission: true,
            requestSoundPermission: true,
            onDidReceiveLocalNotification: onDidReceiveLocalNotification));
    await flutterLocalNotificationsPlugin.initialize(initializationSettings,
        onDidReceiveNotificationResponse: onDidReceiveNotificationResponse,
        onDidReceiveBackgroundNotificationResponse: onDidReceiveBackgroundNotificationResponse);
  }

  void showNotification({required int id, required String body, required String title}) async {
    String payload = "";

    body = body;
    //payload = "myLifestyle_$body";
    await flutterLocalNotificationsPlugin.cancel(id);

    await flutterLocalNotificationsPlugin.show(
      id,
      title,
      body,
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'id',
          'channel',
          importance: Importance.min,
          ongoing: true,
          styleInformation: BigTextStyleInformation(''),
          // icon: '@mipmap/ic_launcher',
        ),
        iOS: DarwinNotificationDetails(),
      ),
      payload: payload,
    );
  }

  // on response action
  void onDidReceiveNotificationResponse(NotificationResponse payload) async {
    // display a dialog with the notification details, tap ok to go to another page
    if (payload != null) {
      print('notification payload: ${payload.payload}');
    }
  }

  static void onDidReceiveBackgroundNotificationResponse(NotificationResponse payload) async {
    // display a dialog with the notification details, tap ok to go to another page
    print("BG notification detected");
    if (payload != null) {
      print('notification payload: ${payload.payload}');
    }
  }

  void onDidReceiveLocalNotification(int? id, String? title, String? body, String? payload) async {
    // display a dialog with the notification details, tap ok to go to another page
  }
}

import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';

ScaffoldMessengerState customSnackBar({
  required BuildContext context,
  required String message,
  String? actionButtonText,
  int snackBarDuration = 4,
  VoidCallback? onPressed,
}) {
  return ScaffoldMessenger.of(context)..removeCurrentSnackBar()..showSnackBar(SnackBar(
    content: Text(
      message,
      style: context.textTheme.bodyMedium!.copyWith(color: context.themeColors.mediumBlackColor),
    ),
    duration: Duration(seconds: snackBarDuration),
    backgroundColor: context.themeColors.snackbarBGColor,
    elevation: 10,
    behavior: SnackBarBehavior.floating,
    margin: const EdgeInsets.only(left: 10, right: 10, bottom: 10),
    action: actionButtonText != null
        ? SnackBarAction(
            label: actionButtonText.toUpperCase(),
            onPressed: () {
              ScaffoldMessenger.of(context).removeCurrentSnackBar();
              onPressed?.call();
            },
            textColor: context.themeColors.mediumBlackColor,
          )
        : null,
  ));
}

void hideSnackBar(BuildContext context) {
  ScaffoldMessenger.of(context).hideCurrentSnackBar();
}
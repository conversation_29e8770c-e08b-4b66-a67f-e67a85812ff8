import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';

import '../../utils/appsize.dart';
import '../../utils/colors/app_colors.dart';

class Loader {
  static showLoaderDialog(BuildContext context) {
    AlertDialog alert = AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(
          Radius.circular(AppSize.r4),
        ),
      ),
      insetPadding: EdgeInsets.symmetric(horizontal: AppSize.w90),
      contentPadding: EdgeInsets.symmetric(vertical: AppSize.h2, horizontal: AppSize.w6),
      backgroundColor: Colors.grey[100]?.withOpacity(0.9),
      content: WillPopScope(
        onWillPop: () async {
          return false;
        },
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: AppSize.w10),
          child: FittedBox(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Image.asset(
                  'assets/gif/loader_gif.gif',
                  height: AppSize.h60,
                  width: AppSize.w60,
                  color: AppColors.primaryColor,
                ),
                Flexible(
                  child: Text(
                    AppLocalizations.of(context)!.pleaseWait,
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontSize: AppSize.sp16,
                          fontWeight: FontWeight.normal,
                          color: AppColors.black,
                        ),
                  ),
                ),
                SpaceH(AppSize.h10)
              ],
            ),
          ),
        ),
      ),
    );
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }

  static closeLoadingDialog(BuildContext context) {
    Navigator.pop(context);
  }
}

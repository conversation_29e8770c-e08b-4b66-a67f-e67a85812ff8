import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/activity_exclusions_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/activitylist_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/activity_exclusions_list.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/activity_list_repository.dart';

part 'select_activity_state.dart';

class SelectActivityCubit extends Cubit<SelectActivityState> {
  SelectActivityCubit() : super(SelectActivityInitial());

  List<ActivityListResponseModel> activityList = [];
  List<ActivityExclusionsListResponseModel> activityExclusionsList = [];
  List<ActivityListResponseModel> timeBondFalseList = [];
  String selectedActivityName = '';
  String selectedActivityId = '';
  List<ActivityListResponseModel> filterActivityList = [];
  ValueNotifier<bool> isLoading = ValueNotifier(false);

  Future<void> setActivityData(
      {required String selectedValue,
      required String selectedId,
      required BuildContext context}) async {
    filterActivityList = List.from(activityList);
    selectedActivityName = selectedValue;
    selectedActivityId = selectedId;
    emit(SelectActivityInitial());
  }

  Future<void> costCentersActivityApiCall(
      {required BuildContext context, required String selectedDate}) async {
    print("api started =====>");
    final ActivityListApiRepository activityListApiRepository =
        ActivityListApiRepository();
    final response = await activityListApiRepository.activityListApi(
      context: context,
      selectedDate: selectedDate,
    );
    print("api done =====>${response}");

    if (response!.isNotEmpty) {
      timeBondFalseList.clear();
      activityList.clear();

      List<ActivityListResponseModel> unFilteredActivityListTemp = response;
      List<ActivityListResponseModel> unFilteredActivityList = [];
      for (var element in unFilteredActivityListTemp) {
        bool isExcluded = activityExclusionsList
            .any((exclusion) => exclusion.costCenterId == element.costCenterId);
        if (!isExcluded) {
          unFilteredActivityList.add(element);
        }
      }
      unFilteredActivityList.forEach((element) {
        if (element.timeBound == false && element.valid == true) {
          timeBondFalseList.add(element);
        }
        if (element.productive == true &&
            element.valid == true &&
            element.timeBound == true) {
          activityList.add(element);
        }
      });

      unFilteredActivityList.forEach((element) {
        if (element.productive == false &&
            element.valid == true &&
            element.timeBound == true) {
          activityList.add(element);
        }
      });

      print("timeBondFalseList ${timeBondFalseList.length}");
      print("timeBondFalseList ${activityList.length}");
    } else {
      // customSnackBar(context: context, message: AppLocalizations.of(context)!.errorText,actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
    }
    emit(SelectActivityInitial());
  }

  Future<void> activityExclusionsApiCall(
      {required BuildContext context}) async {
    print("api started =====>");
    final ActivityExclusionsApiRepository activityExclusionsApiRepository =
        ActivityExclusionsApiRepository();
    final response = await activityExclusionsApiRepository
        .activityExclusionsListApi(context: context);
    print("api done =====>${response}");

    if (response!.isNotEmpty) {
      activityExclusionsList.clear();
      activityExclusionsList.addAll(response);
      print("activityExclusionsList: $activityExclusionsList");
    } else {
      // customSnackBar(context: context, message: AppLocalizations.of(context)!.errorText,actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
    }
    emit(SelectActivityInitial());
  }

  void filterActivity(String query) {
    filterActivityList = activityList
        .where((element) =>
            element.title.toLowerCase().contains(query.toLowerCase()))
        .toList();
    emit(SelectActivityInitial());

    print("filterValue=====>$filterActivityList");
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/schedule_module/widget/schedule_month_shimmer.dart';
import 'package:staff_medewerker/utils/constant/constant.dart';

import '../../../../utils/appsize.dart';
import '../../bloc/schedule_cubit.dart';
import 'month_time_widget.dart';

class ScheduleMonthScreen extends StatefulWidget {
  ScheduleMonthScreen({Key? key}) : super(key: key);

  @override
  State<ScheduleMonthScreen> createState() => _ScheduleMonthScreenState();
}

class _ScheduleMonthScreenState extends State<ScheduleMonthScreen> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      await BlocProvider.of<ScheduleCubit>(context).fetchMonthData(context);
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ScheduleCubit, ScheduleState>(
      builder: (ctx, state) {
        final scheduleBloc = ctx.read<ScheduleCubit>();

        return Container(
          color: context.themeColors.homeContainerColor,
          child: Column(
            children: [
              Expanded(
                  child: ValueListenableBuilder(
                valueListenable: scheduleBloc.isScheduleMonthLoading,
                builder: (context, isScheduleMonthLoading, child) {
                  if (!isScheduleMonthLoading) {
                    if (scheduleBloc.scheduleMonthList1.isNotEmpty) {
                      return ListView.builder(
                        itemCount: scheduleBloc.scheduleMonthList1.length,
                        itemBuilder: (context, index) {
                          DateTime currentISODate = scheduleBloc.scheduleMonthList1[index].iSODate.toString().isNotEmpty
                              ? DateTime.parse(scheduleBloc.scheduleMonthList1[index].iSODate.toString())
                              : DateTime.now();
                          bool isDifferentDate = index == 0 ||
                              currentISODate !=
                                  DateTime.parse(scheduleBloc.scheduleMonthList1[index - 1].iSODate.toString());

                          if (isDifferentDate) {
                            return Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  color: context.themeColors.listWeekGridColor,
                                  height: AppSize.h30,
                                  padding: EdgeInsets.only(left: AppSize.sp14),
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    DateFormat('EEEE d MMMM', appDB.language).format(currentISODate),
                                    style: context.textTheme.bodyMedium?.copyWith(
                                      fontSize: AppSize.sp15,
                                      color: context.themeColors.textColor,
                                    ),
                                  ),
                                ),
                                MonthDetailWidget(
                                  index: index,
                                ),
                              ],
                            );
                          } else {
                            return MonthDetailWidget(
                              index: index,
                            );
                          }
                        },
                      );
                    } else {
                      return Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Container(
                            color: context.themeColors.listGridColor1,
                            alignment: Alignment.centerLeft,
                            child: Center(
                              child: Padding(
                                padding: EdgeInsets.symmetric(vertical: AppSize.h4),
                                child: Text(AppLocalizations.of(context)!.noShifts,
                                    style: context.textTheme.bodyMedium
                                        ?.copyWith(fontSize: AppSize.sp15, color: context.themeColors.textColor)),
                              ),
                            ),
                          ),
                        ],
                      );
                    }
                  } else {
                    return ScheduleMonthShimmerWidget();
                  }
                },
              ))
            ],
          ),
        );
      },
    );
  }
}

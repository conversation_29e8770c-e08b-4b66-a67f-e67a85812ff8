import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/schedule_module/bloc/schedule_cubit.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

import '../../../../utils/colors/app_colors.dart';
import '../../bloc/schedule_date_picker_cubit.dart';
import '../../model/schedule_month_model.dart';
import '../month_screens/schedule_month_time_detail_screen.dart';

class ScheduleCalenderScreen extends StatefulWidget {
  ScheduleCalenderScreen({Key? key}) : super(key: key);

  @override
  State<ScheduleCalenderScreen> createState() => _ScheduleCalenderScreenState();
}

class _ScheduleCalenderScreenState extends State<ScheduleCalenderScreen> {
  List dateMonth = [];
  Map<DateTime, List<ScheduleMonthResponseModel>> dateToData = {};
  List<ScheduleMonthResponseModel> selectedData = [];
  bool isSelectedData1 = false;
  CalendarController _controller = CalendarController();

  @override
  Widget build(BuildContext context) {
    void showDetailedItem(ScheduleMonthResponseModel item) {
      AppNavigation.nextScreen(
        context,
        ScheduleMonthTimeDetailScreen(scheduleMonthData: item),
      );
    }

    print('aaa------------>${MediaQuery.of(context).size.height * 0.75}');
    return BlocBuilder<ScheduleCubit, ScheduleState>(
      builder: (ctx, state) {
        final scheduleBloc = ctx.read<ScheduleCubit>();
        _controller.displayDate = scheduleBloc.selectDate;
        return Padding(
          padding: EdgeInsets.symmetric(
            horizontal: 0,
          ),
          child: FittedBox(
            child: Container(
              color: context.themeColors.homeContainerColor,
              //color: Colors.red,
              height: MediaQuery.of(context).size.height * 0.75,

              width: MediaQuery.of(context).size.width,
              child: MediaQuery(
                data: MediaQuery.of(context).removePadding(removeTop: true),
                child: SfCalendar(
                  controller: _controller,
                  view: CalendarView.month,
                  firstDayOfWeek: DateTime.monday,
                  cellBorderColor: AppColors.transparent,
                  headerHeight: 7, // Set the header height to 0 to remove top padding

                  viewHeaderStyle: ViewHeaderStyle(
                    backgroundColor: Colors.transparent,
                    dayTextStyle: context.textTheme.bodyMedium?.copyWith(
                      color: context.themeColors.textColor,
                      fontSize: AppSize.sp14,
                    ),
                  ),
                  todayTextStyle: context.textTheme.bodyMedium?.copyWith(
                    color: context.themeColors.textColor,
                    fontSize: AppSize.sp13,
                  ),
                  headerStyle: CalendarHeaderStyle(
                      textStyle: context.textTheme.bodyMedium?.copyWith(
                    color: AppColors.transparent,
                    fontSize: AppSize.sp13,
                  )),
                  todayHighlightColor: AppColors.transparent,
                  selectionDecoration: BoxDecoration(
                    color: AppColors.transparent,
                    shape: BoxShape.rectangle,
                  ),
                  dataSource: _getCalendarDataSource(),
                  
                  onSelectionChanged: (CalendarSelectionDetails calendarSelectionDetails) {
                    print('onSelectionChanged 1----------->${_controller.displayDate}');
                    print('onSelectionChanged 1----------->${_controller.displayDate}');
                    print('onSelectionChanged 1----------->${calendarSelectionDetails.date}');
                    print('onSelectionChanged 1----------->${calendarSelectionDetails.date?.day}');
                    setState(() {
                      scheduleBloc.selectDate = calendarSelectionDetails.date ?? DateTime.now(); // Update selectedDate
                    });
                    DateFormat formatter = DateFormat('yyyy-MM-dd');
                    String formattedSelectedDay = formatter.format(scheduleBloc.selectDate);
                    scheduleBloc.isDayInList = scheduleBloc.scheduleDayList1.any((date) {
                      String formattedDay = formatter.format(DateTime.parse(date.date ?? ''));
                      log('formattedDay ------------->${formattedDay}');

                      return formattedDay == formattedSelectedDay;
                    });
                    scheduleBloc.updateDayList(scheduleBloc.isDayInList);
                    if (scheduleBloc.isDayInList) {
                      List<ScheduleMonthResponseModel> matchingItems = [];
                      log('------------->${selectedData}');

                      if (isSelectedData1 == true) {
                        for (var scheduleItem in selectedData) {
                          String formattedItemDay = formatter.format(DateTime.parse(scheduleItem.date ?? ''));
                          if (formattedItemDay == formattedSelectedDay) {
                            matchingItems.add(scheduleItem);
                          }
                        }
                      } else {
                        for (var scheduleItem in scheduleBloc.scheduleDayList1) {
                          String formattedItemDay = formatter.format(DateTime.parse(scheduleItem.date ?? ''));
                          if (formattedItemDay == formattedSelectedDay) {
                            matchingItems.add(scheduleItem);
                          }
                        }
                      }
                      if (matchingItems.isNotEmpty) {
                        if (matchingItems.length > 1) {
                          print('showModalBottomSheet----------->');
                          showModalBottomSheet(
                            context: context,
                            builder: (context) {
                              return Container(
                                height: AppSize.h90,
                                child: ListView.builder(
                                  itemCount: matchingItems.length,
                                  itemBuilder: (context, index) {
                                    return ListTile(
                                      title: Text(
                                        '${matchingItems[index].timeFrom.toString()}-${matchingItems[index].timeUntil.toString()} ${matchingItems[index].department.toString()}',
                                        style: context.textTheme.bodyMedium?.copyWith(
                                          color: context.themeColors.textColor,
                                          fontSize: AppSize.sp14,
                                        ),
                                      ),
                                      // trailing: (matchingItems[index].swap?.state != null)
                                      //     ? Icon(
                                      //         Ionicons.swap_horizontal_outline,
                                      //         size: AppSize.sp20,
                                      //         color: AppColors.primaryColor,
                                      //       )
                                      //     : (matchingItems[index].openService != null)
                                      //         ? Icon(
                                      //             Ionicons.create_outline,
                                      //             size: AppSize.sp20,
                                      //             color: AppColors.primaryColor,
                                      //           )
                                      //         : Container(),
                                      onTap: () {
                                        Navigator.pop(context); // Close the bottom sheet
                                        showDetailedItem(matchingItems[index]);
                                      },
                                    );
                                  },
                                ),
                              );
                            },
                          );
                        } else {
                          showDetailedItem(matchingItems[0]);
                        }
                      }
                    } else {
                      print('1----------->');
                    }
                  },
                  cellEndPadding: 5,
                  onViewChanged: (viewChangedDetails) {
                    SchedulerBinding.instance.addPostFrameCallback((duration) async {
                      scheduleBloc.selectDate = viewChangedDetails.visibleDates.first;
                      ctx.read<ScheduleTimeCubit>().setDate(selectedValue: scheduleBloc.selectDate);
                      setState(() {});
                      if (!dateToData.containsKey(scheduleBloc.selectDate)) {
                        log("Date is not in dateToData, fetching data.");
                        isSelectedData1 = false;
                        await BlocProvider.of<ScheduleCubit>(context).fetchCalendarData(context);
                        if (scheduleBloc.scheduleDayList.isNotEmpty) {
                          //scheduleBloc.scheduleDayList1 = scheduleBloc.scheduleDayList;
                          scheduleBloc.filterApplicationReferenceListForCalendar();
                          List<ScheduleMonthResponseModel> copy = List.of(scheduleBloc.scheduleDayList);
                          dateToData[scheduleBloc.selectDate] = copy;
                          log("Data saved in dateToData for ${scheduleBloc.selectDate}");
                        }
                      } else {
                        isSelectedData1 = true;
                        selectedData = dateToData[scheduleBloc.selectDate]!;
                        log("Selected data retrieved from dateToData.${selectedData}");

                        if (selectedData.isNotEmpty) {
                          // scheduleBloc.scheduleDayList1 = selectedData!;
                          if (scheduleBloc.selectedTitles.value == 'Rooster, Ruilen, Open diensten') {
                            scheduleBloc.scheduleDayList1 = selectedData;
                          } else if (scheduleBloc.selectedTitles.value.contains('Ruilen') &&
                              scheduleBloc.selectedTitles.value.contains('Open diensten')) {
                            scheduleBloc.scheduleDayList1 = selectedData.where(
                              (data) {
                                print("State: ${data.swap?.state}");
                                return data.openService is OpenServiceModel ||
                                    (data.swap is SwapMonthModel &&
                                        (data.swap?.state == 'Aangevraagd' || data.swap?.state == 'Uitgezet'));
                              },
                            ).toList();
                          } else if (scheduleBloc.selectedTitles.value.contains('Ruilen') &&
                              scheduleBloc.selectedTitles.value.contains('Rooster')) {
                            scheduleBloc.scheduleDayList1 = selectedData.where(
                              (data) {
                                print("State: ${data.swap?.state}");
                                return ((data.swap?.state == 'Aangevraagd' ||
                                    data.swap?.state == 'Uitgezet' ||
                                    data.swap?.state == null));
                              },
                            ).toList();
                          } else if (scheduleBloc.selectedTitles.value.contains('Rooster') &&
                              scheduleBloc.selectedTitles.value.contains('Open diensten')) {
                            scheduleBloc.scheduleDayList1 = selectedData.where(
                              (data) {
                                print("State: ${data.swap?.state}");
                                return data.openService is OpenServiceModel || (data.swap?.state == null);
                              },
                            ).toList();
                          } else if (scheduleBloc.selectedTitles.value == 'Open diensten') {
                            scheduleBloc.scheduleDayList1 =
                                selectedData.where((data) => data.openService is OpenServiceModel).toList();
                          } else if (scheduleBloc.selectedTitles.value == 'Ruilen') {
                            scheduleBloc.scheduleDayList1 = selectedData
                                .where((data) =>
                                    data.swap is SwapMonthModel &&
                                    (data.swap?.state == 'Aangevraagd' || data.swap?.state == 'Uitgezet'))
                                .toList();
                          } else if (scheduleBloc.selectedTitles.value == 'Rooster') {
                            scheduleBloc.scheduleDayList1 = selectedData.where((data) {
                              print("Swap: ${data.swap}");
                              print("State: ${data.swap?.state}");
                              return data.swap is SwapMonthModel && (data.swap?.state == null);
                            }).toList();
                          } else {
                            //scheduleMonthList1 = scheduleMonthList;
                          }
                          log("Selected data retrieved from dateToData.${selectedData}");
                        } else {}
                        setState(() {});
                      }
                    });
                  },

                  appointmentTextStyle: TextStyle(
                      color: navigatorKey.currentContext!.themeColors.homeContainerColor,
                      fontSize: AppSize.sp12,
                      height: 1, // Adjust height to reduce vertical space for appointment text

                      fontWeight: FontWeight.w500),

                  monthViewSettings: MonthViewSettings(
                    appointmentDisplayCount: 4,
                    appointmentDisplayMode: MonthAppointmentDisplayMode.appointment,
                    monthCellStyle: MonthCellStyle(
                      todayBackgroundColor: context.themeColors.calendarTodayDateColor,
                      textStyle: context.textTheme.bodyMedium?.copyWith(
                        color: context.themeColors.textColor,
                        fontSize: AppSize.sp14,
                      ),
                    ),
                    showTrailingAndLeadingDates: false,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

DataSource _getCalendarDataSource() {
  final scheduleBloc = BlocProvider.of<ScheduleCubit>(navigatorKey.currentContext!);
  final List<Appointment> appointments = <Appointment>[];
  List<String?> temp = [];
  Map<String, int> dateCount = {}; // Map to store date counts

  scheduleBloc.scheduleDayList1.forEach((element) {
    // log(element.toJson().toString(), name: 'full Model');
    // log('=================>>>', name: 'full Model');
    if (element.date != null) {
    // log((element.swap!=null?element.swap!.toJson().toString() : 'no swap'), name: 'full Model swap: ');

      if (!temp.contains(element.date)) {
        print("------->${element.date}");
        // print("------->${element.timeFrom}");
        // print("------->${element.timeUntil}");

        int count = dateCount[element.date] ?? 0; // Get the count for the date

        appointments.add(Appointment(
          startTime: DateTime.parse(element.date!),
          endTime: DateTime.parse(element.date!),
          subject: element.costCenters ?? 's',
          color: Color.fromRGBO(152, 154, 162, 1),
        ));

        appointments.add(Appointment(
          startTime: DateTime.parse(element.date!),
          endTime: DateTime.parse(element.date!),
          subject: element.timeFrom ?? '',
          color: Color.fromRGBO(152, 154, 162, 1),
        ));

        appointments.add(Appointment(
          startTime: DateTime.parse(element.date!),
          endTime: DateTime.parse(element.date!),
          subject: element.timeUntil ?? '',
          color: Color.fromRGBO(152, 154, 162, 1),
        ));

        temp.add(element.date);
        dateCount[element.date ?? ''] = count + 1;
      } else {
        int count = dateCount[element.date] ?? 0;

        appointments.add(Appointment(
          startTime: DateTime.parse(element.date!),
          endTime: DateTime.parse(element.date!),
          subject: '+${count}',
          color: AppColors.primaryColor,
        ));
      }
    }
  });

  return DataSource(appointments);
}

class DataSource extends CalendarDataSource {
  DataSource(List<Appointment> source) {
    appointments = source;
  }
}

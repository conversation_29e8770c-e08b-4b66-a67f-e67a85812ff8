import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';

import '../../../../common/custom_widgets/appbar_custom.dart';
import '../../../../common/custom_widgets/spacebox.dart';
import '../../../../utils/appsize.dart';
import '../../../profile_module/widget/common/common_info_row.dart';
import '../../model/schedule_month_model.dart';


class ScheduleCalendarTimeDetailScreen extends StatelessWidget {
  final ScheduleMonthResponseModel? scheduleMonthData;

  const ScheduleCalendarTimeDetailScreen({Key? key, this.scheduleMonthData}) : super(key: key);

  @override
  Widget build(BuildContext context) {

    log("navigate date=======>${scheduleMonthData}");
    return Scaffold(
      appBar: CustomAppBar(title: DateFormat('EEEE d MMMM').format(
          DateTime.parse(scheduleMonthData?.iSODate.toString() ?? ''))),
      backgroundColor: context.themeColors.homeContainerColor,

      body: Padding(
        padding: const EdgeInsets.only(top: 20,left: 20,right: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SpaceV(AppSize.h10),
            CommonInfoRow(title: AppLocalizations.of(context)!.startScheduleTime,value: scheduleMonthData?.timeFrom.toString() ?? '',titleValue: context.textTheme.titleMedium?.copyWith(
                fontSize: AppSize.sp13,
                color: context.themeColors.darkGreyColor),titlePadding: 0,),
            CommonInfoRow(title: AppLocalizations.of(context)!.endTime,value: scheduleMonthData?.timeUntil.toString() ?? '-',titleValue: context.textTheme.titleMedium?.copyWith(
                fontSize: AppSize.sp13,
                color: context.themeColors.darkGreyColor),titlePadding: 0,),
            CommonInfoRow(title: AppLocalizations.of(context)!.department,value: scheduleMonthData?.department ?? '-',titleValue: context.textTheme.titleMedium?.copyWith(
                fontSize: AppSize.sp13,
                color: context.themeColors.darkGreyColor),titlePadding: 0,),
            CommonInfoRow(title: AppLocalizations.of(context)!.breakScheduleText,value:scheduleMonthData?.breakTime ?? '-',titleValue: context.textTheme.titleMedium?.copyWith(
                fontSize: AppSize.sp13,
                color: context.themeColors.darkGreyColor),titlePadding: 0,),
            CommonInfoRow(title: AppLocalizations.of(context)!.costCenter,value: scheduleMonthData?.costCenters ?? '-',titleValue: context.textTheme.titleMedium?.copyWith(
                fontSize: AppSize.sp13,
                color: context.themeColors.darkGreyColor),titlePadding: 0,),
            CommonInfoRow(title: AppLocalizations.of(context)!.service,value: scheduleMonthData?.service ?? '-',titleValue: context.textTheme.titleMedium?.copyWith(
                fontSize: AppSize.sp13,
                color: context.themeColors.darkGreyColor),titlePadding: 0,),
            CommonInfoRow(title: AppLocalizations.of(context)!.remark,value: scheduleMonthData?.remark ?? '-',titleValue: context.textTheme.titleMedium?.copyWith(
                fontSize: AppSize.sp13,
                color: context.themeColors.darkGreyColor),titlePadding: 0,),
            CommonInfoRow(title: AppLocalizations.of(context)!.dayRemark,value: scheduleMonthData?.dayRemark ?? '-',titleValue: context.textTheme.titleMedium?.copyWith(
                fontSize: AppSize.sp13,
                color: context.themeColors.darkGreyColor),titlePadding: 0,),
          ],
        ),
      ),
    );
  }
}

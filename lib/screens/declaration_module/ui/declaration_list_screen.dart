import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/declaration_module/cubit/declration_cubit.dart';
import 'package:staff_medewerker/screens/declaration_module/model/declaration_type.dart';
import 'package:staff_medewerker/screens/declaration_module/ui/mileage_register_screen.dart';
import 'package:staff_medewerker/screens/declaration_module/ui/voucher_photo_upload_screen.dart';
import 'package:staff_medewerker/screens/home_module/ui/home_screen.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/asset_path/assets_path.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';
import 'package:staff_medewerker/utils/date_language_convter.dart';

import '../../home_module/ui/bottom_bar_screen.dart';

class DeclarationListScreen extends StatefulWidget {
  const DeclarationListScreen({super.key});

  @override
  State<DeclarationListScreen> createState() => _DeclarationListScreenState();
}

class _DeclarationListScreenState extends State<DeclarationListScreen> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => DeclrationCubit(),
      child: BlocBuilder<DeclrationCubit, DeclrationState>(
        builder: (ctx, state) {
          final ref = ctx.read<DeclrationCubit>();
          return Scaffold(
            floatingActionButton: FabWithIcons(
              onIconTapped: (index) {
                log('index$index');
                index == 0
                    ? AppNavigation.nextScreen(
                        context,
                        BlocProvider.value(
                            value: ref,
                            child: VoucherPhotoUploadScreen(
                              index: index,
                              guid: ref.declarationTypeList[index].guid
                                  .toString(),
                            )))
                    : AppNavigation.nextScreen(
                        context,
                        BlocProvider.value(
                            value: ref,
                            child: MileageRegisterScreen(
                              guid: ref.declarationTypeList[index].guid
                                  .toString(),
                            )));
              },
            ),
            appBar: CustomAppBar(
                isLeading: true,
                onTap: () {
                  log('message onTap1');

                  AppNavigation.replaceScreen(
                      context,
                      BottomBarScreen(
                        isApiCall: false,
                      )).then((val) {
                    AppNavigation.previousScreen(context);
                  });
                },
                title: AppLocalizations.of(context)!.declaration),
            body: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: AppSize.w18,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SpaceV(AppSize.h14),
                  Text(AppLocalizations.of(context)!.overview,
                      style: context.textTheme.titleMedium?.copyWith(
                        color: context.themeColors.textColor,
                      )),
                  SpaceV(AppSize.h14),
                  Expanded(
                    child: Stack(
                      children: [
                        ValueListenableBuilder(
                          valueListenable: ref.isLoading,
                          builder: (context, value, child) {
                            if (value) {
                              return Center(
                                  child: CupertinoActivityIndicator());
                            } else if (!value && ref.declrationList.isEmpty) {
                              return Center(
                                child: Text(
                                    AppLocalizations.of(context)!
                                        .declarationsNotFound,
                                    style: context.textTheme.bodySmall
                                        ?.copyWith(
                                            color:
                                                context.themeColors.textColor,
                                            fontSize: AppSize.sp13)),
                              );
                            } else {
                              return ListView.builder(
                                itemCount: ref.declrationList.length,
                                itemBuilder: (context, index) {
                                  print(
                                      '====>>${ref.declrationList[index].toJson()}');
                                  double roundedValue = double.tryParse(ref
                                          .declrationList[index].costs!
                                          .toStringAsFixed(2)) ??
                                      0.00;

                                  final formatter = NumberFormat.currency(
                                    locale: 'de',
                                    symbol: '€', // Currency symbol
                                    decimalDigits:
                                        2, // Set to 2 decimal places for rounding
                                  );

                                  return Column(
                                    children: [
                                      Padding(
                                        padding: EdgeInsets.only(
                                            top: index == 0
                                                ? AppSize.h10
                                                : AppSize.h6,
                                            bottom: index ==
                                                    ref.declrationList.length -
                                                        1
                                                ? AppSize.h40
                                                : AppSize.h6),
                                        child: Container(
                                          child: Column(
                                            children: [
                                              Row(
                                                children: [
                                                  ref.declrationList[index].type
                                                              ?.toLowerCase() ==
                                                          'bon'
                                                      ? SvgPicture.asset(
                                                          AssetsPath
                                                              .receiptIcon,
                                                          height: AppSize.w28,
                                                          width: AppSize.w28,
                                                          colorFilter:
                                                              ColorFilter.mode(
                                                                  Color(
                                                                      0xff05714B),
                                                                  BlendMode
                                                                      .srcIn),
                                                        )
                                                      : Image.asset(
                                                          AssetsPath.vectorIcon,
                                                          height: AppSize.w28,
                                                          width: AppSize.w28,
                                                        ),
                                                  SpaceH(AppSize.w10),
                                                  Expanded(
                                                    flex: 10,
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Text(
                                                            ref.declrationList[index].type
                                                                        ?.toLowerCase() ==
                                                                    'bon'
                                                                ? "Bon"
                                                                : (ref
                                                                        .declrationList[
                                                                            index]
                                                                        .description ??
                                                                    ""),
                                                            maxLines: 2,
                                                            overflow:
                                                                TextOverflow
                                                                    .ellipsis,
                                                            style: context
                                                                .textTheme
                                                                .bodySmall
                                                                ?.copyWith(
                                                                    color: context
                                                                        .themeColors
                                                                        .textColor,
                                                                    fontSize:
                                                                        AppSize
                                                                            .sp13)),
                                                        SpaceV(AppSize.h4),
                                                        Text(
                                                            DateLanguageConverter
                                                                .convertEnglishToDutch(
                                                              DateFormat('dd MMM yyyy').format(DateTime.parse(DateFormat(
                                                                      'yyyy-MM-ddTHH:mm:ss')
                                                                  .parse(ref
                                                                          .declrationList[
                                                                              index]
                                                                          .date ??
                                                                      "")
                                                                  .toString())),
                                                            ),
                                                            style: context
                                                                .textTheme
                                                                .bodySmall
                                                                ?.copyWith(
                                                                    color: context
                                                                        .themeColors
                                                                        .darkGreyColor,
                                                                    fontSize:
                                                                        AppSize
                                                                            .sp13)),
                                                      ],
                                                    ),
                                                  ),
                                                  Spacer(),
                                                  Text(
                                                      ref.declrationList[index].type
                                                                  ?.toLowerCase() ==
                                                              'bon'
                                                          ? '€${formatter.format(roundedValue).replaceAll("€", " ").trim()}'
                                                          : '${ref.declrationList[index].kilometers} KM',
                                                      style: context
                                                          .textTheme.bodySmall
                                                          ?.copyWith(
                                                              color: context
                                                                  .themeColors
                                                                  .textColor,
                                                              fontSize:
                                                                  AppSize.sp14,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w600)),
                                                  SpaceH(AppSize.w14),
                                                  CircleAvatar(
                                                    radius: AppSize.sp8,
                                                    backgroundColor: ref
                                                                .declrationList[
                                                                    index]
                                                                .state ==
                                                            'Goedgekeurd'
                                                        ? Color.fromRGBO(
                                                            75, 197, 0, 1)
                                                        : ref.declrationList[index].state ==
                                                                'Ingediend'
                                                            ? Color.fromRGBO(
                                                                255, 169, 1, 1)
                                                            : ref.declrationList[index].state ==
                                                                    'Afgekeurd'
                                                                ? Color.fromRGBO(
                                                                    255,
                                                                    1,
                                                                    1,
                                                                    1)
                                                                : ref.declrationList[index].state ==
                                                                        'Verwerkt'
                                                                    ? Colors
                                                                        .blue
                                                                    : Color.fromRGBO(
                                                                        255,
                                                                        169,
                                                                        1,
                                                                        1),
                                                  )
                                                ],
                                              ),
                                              Padding(
                                                padding: EdgeInsets.only(
                                                    left: AppSize.w36),
                                                child: Divider(
                                                  color: context
                                                      .themeColors.greyColor,
                                                ),
                                              )
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  );
                                },
                              );
                            }
                          },
                        ),
                        ValueListenableBuilder(
                          valueListenable: ref.isTypeLoading,
                          builder: (context, value, child) {
                            if (value) {
                              return Container(
                                decoration: BoxDecoration(
                                    // color: AppColors.white.withOpacity(0.3)
                                    ),
                                child: Center(
                                    child: CupertinoActivityIndicator(
                                  radius: 10,
                                )),
                              );
                            } else {
                              return SizedBox();
                            }
                          },
                        )
                      ],
                    ),
                  )
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

class FabWithIcons extends StatefulWidget {
  FabWithIcons({required this.onIconTapped});
  final ValueChanged<int> onIconTapped;
  @override
  State<FabWithIcons> createState() => FabWithIconsState();
}

class FabWithIconsState extends State<FabWithIcons>
    with TickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    final declarationProvider =
        BlocProvider.of<DeclrationCubit>(context, listen: false);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      declarationProvider.getDeclarationTypeData(context: context);
    });
    _controller =
        AnimationController(vsync: this, duration: Duration(milliseconds: 250));

    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DeclrationCubit, DeclrationState>(builder: (ctx, state) {
      final ref = ctx.read<DeclrationCubit>();
      return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            ...List.generate(ref.declarationTypeList.length, (int index) {
              return _buildChild(index, ref.declarationTypeList, ref);
            }).toList(),
            _buildFab(ref),
          ]
          /*List.generate(ref.declarationTypeList.length, (int index) {
            return _buildChild(index, ref.declarationTypeList,ref);
          }).toList()
            ..add(
              _buildFab(ref),
            ),*/
          );
    });
  }

  Widget _buildChild(
      int index, List<DeclarationTypeModel> dTypeList, DeclrationCubit ref) {
    return Container(
      height: AppSize.h36,
      width: AppSize.w70,
      alignment: FractionalOffset.topCenter,
      child: ScaleTransition(
        scale: CurvedAnimation(
          parent: _controller,
          curve: Interval(0.0, 1.0 - index / dTypeList.length / 2.0,
              curve: Curves.easeOut),
        ),
        child: GestureDetector(
          onTap: () {
            widget.onIconTapped(index);
          },
          child: Container(
            height: AppSize.h26,
            width: AppSize.w80,
            decoration: BoxDecoration(
                color: context.themeColors.declarationTypeColor,

                //color: Color.fromRGBO(224, 224, 224, 1),
                borderRadius: BorderRadius.circular(AppSize.r16)),
            child: Center(
              child: Text(dTypeList[index].Title.toString(),
                  style: context.textTheme.bodySmall?.copyWith(
                      color: context.themeColors.textColor,
                      fontSize: AppSize.sp12,
                      fontWeight: FontWeight.w600)),
            ),
            // backgroundColor: backgroundColor,
            // mini: true,
            // // child: Icon(widget.icons[index], color: foregroundColor),
            // onPressed: () => _onTapped(index),
          ),
        ),
      ),
    );
  }

  Widget _buildFab(DeclrationCubit ref) {
    return FloatingActionButton(
      onPressed: () {
        if (_controller.isDismissed) {
          _controller.forward();
          ref.getDeclarationTypeData(context: context);
        } else {
          _controller.reverse();
          // ref.getDeclarationTypeData(context: context);
        }
        setState(() {});
      },
      tooltip: 'Increment',
      backgroundColor: AppColors.primaryColor,
      child: Icon(
        Icons.add,
        color: AppColors.white,
      ),
      elevation: 10,
    );
  }

  void _onTapped(int index) {
    _controller.reverse();
    widget.onIconTapped(index);
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';
import 'package:staff_medewerker/utils/constant/constant.dart';

import '../bloc/notification_cubit.dart';
import 'notification_detail_screen.dart';

class NotificationScreen extends StatelessWidget {
  const NotificationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: AppLocalizations.of(context)!.notifications),
      body: BlocBuilder<NotificationCubit, NotificationState>(
        builder: (ctx, state) {
          final notificationBloc = ctx.read<NotificationCubit>();

          return Column(
            children: [
              SpaceV(AppSize.h26),
              Expanded(
                child: notificationBloc.notificationList.isNotEmpty
                    ? ListView.builder(
                        itemCount: notificationBloc.notificationList.length,
                        itemBuilder: (context, index) {
                          return Padding(
                            padding: EdgeInsets.only(left: AppSize.w20, right: AppSize.w20),
                            child: Column(
                              children: [
                                InkWell(
                                  onTap: () {
                                    notificationBloc.fetchReadNotificationList(
                                        context: context, id: notificationBloc.notificationList[index].id ?? 0);
                                    print('-------->${notificationBloc.notificationList[index].id}');
                                    int notificationId = notificationBloc.notificationList[index].id ?? 0;
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => NotificationDetailScreen(
                                          notificationData: notificationBloc.notificationList[index],
                                          notificationId: notificationId,
                                        ),
                                      ),
                                    ).then((_) {
                                      notificationBloc.removeNotificationList(notificationId);
                                    });
                                  },
                                  child: Container(
                                    child: Padding(
                                      padding: EdgeInsets.only(left: AppSize.w10),
                                      child: Row(
                                        children: [
                                          Icon(
                                            Ionicons.mail,
                                            size: AppSize.sp24,
                                            color: AppColors.primaryColor,
                                          ),
                                          Expanded(
                                            child: Padding(
                                              padding: EdgeInsets.only(left: AppSize.w30),
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                      DateFormat("MM-dd-yy", appDB.language)
                                                          .format(DateTime.parse(notificationBloc
                                                              .notificationList[index].created
                                                              .toString()))
                                                          .toString(),
                                                      style: context.textTheme.bodyMedium
                                                          ?.copyWith(color: context.themeColors.iconColor)),
                                                  Text(
                                                    notificationBloc.notificationList[index].title ??
                                                        'Rooster opnieuw gepubliceerd',
                                                    overflow: TextOverflow.ellipsis,
                                                    maxLines: 2,
                                                    style: context.textTheme.bodyMedium?.copyWith(
                                                      color: context.themeColors.textColor,
                                                      fontSize: AppSize.sp15,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: EdgeInsets.symmetric(vertical: AppSize.h10),
                                  child: Divider(
                                    color: context.themeColors.dividerAvailbilityColor,
                                    height: 0,
                                    thickness: 1.2,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      )
                    : Padding(
                        padding: EdgeInsets.only(
                          left: AppSize.w20,
                        ),
                        child: Text(
                          AppLocalizations.of(context)!.noNewNotificationText,
                          style: context.textTheme.headlineLarge!.copyWith(
                              color: context.themeColors.textColor,
                              fontSize: AppSize.sp15,
                              fontWeight: FontWeight.normal),
                        ),
                      ),
              ),
            ],
          );
        },
      ),
    );
  }
}

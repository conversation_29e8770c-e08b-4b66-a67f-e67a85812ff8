import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../../utils/appsize.dart';
import '../../../utils/colors/app_colors.dart';

class RemarkTextFiled extends StatelessWidget {
  final TextEditingController? controller;
  final TextInputType? keyboardType;
  final String? prefixText;
  final int? maxLines;
  final bool focusBorderColor;
  final TextInputAction? textInputAction;
  final String? initialValue;
  final void Function(String)? onChanged;
  const RemarkTextFiled(
      {Key? key,
      this.controller,
      this.keyboardType,
      this.prefixText,
      this.maxLines,
      this.focusBorderColor = false,
      this.textInputAction, this.initialValue, this.onChanged})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      initialValue: initialValue ,
      controller: controller,
      keyboardType: keyboardType,
      textInputAction: textInputAction,
      onChanged:onChanged,
      decoration: InputDecoration(
        // prefixText: prefixText ?? '',
        prefixIcon: Padding(
          padding: EdgeInsets.symmetric(
              horizontal: AppSize.w10, vertical: AppSize.h10),
          child: Text(prefixText ?? AppLocalizations.of(context)!.remarkDot,
              textAlign: TextAlign.center,
              style: context.textTheme.bodyMedium?.copyWith(
                color: context.themeColors.textColor,
                fontSize: AppSize.sp15,
              )),
        ),

        enabledBorder: UnderlineInputBorder(
          borderSide:
              BorderSide(width: 1, color: context.themeColors.dividerAvailbilityColor),
        ),
        focusedBorder: UnderlineInputBorder(
            borderSide: BorderSide(
                width: 2,
                color: focusBorderColor
                    ? AppColors.limeGreenColor
                    : Colors.transparent)),
        // hintText: hintText,
        hintStyle: context.textTheme.bodyMedium?.copyWith(
            fontSize: AppSize.sp14, color: context.themeColors.greyColor),
        contentPadding: EdgeInsets.symmetric(
            horizontal: AppSize.w10, vertical: AppSize.h10),
      ),
      cursorColor: context.themeColors.lightBlackColor,
    );
  }
}

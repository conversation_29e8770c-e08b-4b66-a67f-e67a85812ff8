import 'dart:convert';
import 'dart:developer';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:image_picker/image_picker.dart';
import 'package:staff_medewerker/common/custom_widgets/common_snackbar.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_loader.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/profile_module/profile_data_repository/profile_data_api_repository.dart';
import 'package:staff_medewerker/screens/profile_module/profile_image_repository/profile_image_api_repository.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';

import '../../../utils/constant/constant.dart';
import '../change_password_module/repository/change_password_api_repository.dart';

class ProfileCubit extends Cubit<bool> {
  ProfileCubit() : super(false);
  ImagePicker picker = ImagePicker();
  XFile? image;
  ValueNotifier<String> imagePath = ValueNotifier('');
  ValueNotifier<bool> inProgress = ValueNotifier(false);
  bool isApiProfileCalled = false;
  Image? getImage;
  ValueNotifier<bool> isImageLoading = ValueNotifier(true);
  String imageString = '';
  final ProfileImageApiRepository profileImageApiRepository = ProfileImageApiRepository();
  Future<void> pickImage({required ImageSource profileImage, required BuildContext context}) async {
    image = await picker.pickImage(source: profileImage);
    imagePath.value = image!.path;
    isImageLoading.value = true;
    print("image: ${image!.path}");

    final response = await profileImageApiRepository.profileImageApi(
        context: navigatorKey.currentContext!, imagePath: imagePath.value);

    // inProgress.value = false;

    if (response!.statusCode == 200 && response.done == true && imagePath != '') {
      print("api done =====>${response.result[0].toString()}");
      customSnackBar(
        context: navigatorKey.currentContext!,
        message: response.result[0].toString(),
        actionButtonText: 'close',
      );

      print("--------------------->${appDB.user?.PhotoId}");
// isImageLoading.value = true;
      final response1 =  await profileImageApiRepository.getProfilePicApi(context: context, guid: appDB.user?.PhotoId ?? '');
      isImageLoading.value = false;
      if (response1?.fileContent != null) {
        final encodedStr = response1?.fileContent;
        Uint8List bytes = base64.decode(encodedStr!);
        getImage = Image.memory(bytes);
        await prefs.setString(AppConstants.cachedImageData, encodedStr);
        await prefs.setString(AppConstants.cachedPhotoId, appDB.user!.PhotoId.toString());
        print("--------------------->${getImage?.image}");
      }
    } else if (response.done == false && imagePath != '') {
      customSnackBar(
        context: navigatorKey.currentContext!,
        message: response.result[0].toString(),
        actionButtonText: 'close',
      );
    } else {
      customSnackBar(
        context: navigatorKey.currentContext!,
        message: AppLocalizations.of(context)!.errorText,
        actionButtonText: 'close',
      );
    }
  }

  Future<void> changePassword(
      {required BuildContext context,
      required String currentPassword,
      required String newPassword,
      required String confirmPassword}) async {
    if (currentPassword.isEmpty || newPassword.isEmpty || confirmPassword.isEmpty) {
      customSnackBar(
        context: context,
        message: AppLocalizations.of(context)!.enterPasswordErrorText,
        actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase(),
      );
    } else if (newPassword != confirmPassword) {
      customSnackBar(
        context: context,
        message: AppLocalizations.of(context)!.passwordNotMatchedText,
        actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase(),
      );
    } else {
      Loader.showLoaderDialog(navigatorKey.currentContext!);
      inProgress.value = true;
      print("api started =====>");
      final ChangePasswordApiRepository changePasswordApiRepository = ChangePasswordApiRepository();
      final response = await changePasswordApiRepository.changePasswordApi(
          context: context, newPassword: newPassword, currentPassword: currentPassword);
      print("api done =====>${response}");
      print("api newPassword =====>$newPassword");
      //EasyLoading.dismiss();
      Loader.closeLoadingDialog(navigatorKey.currentContext!);

      inProgress.value = false;

      if (newPassword != null && response?.statusCode == 200 && response?.done == true && newPassword != '') {
        print("api done =====>${response}");
        print("is password changed =====>${response?.done}");
        customSnackBar(
          context: navigatorKey.currentContext!,
          message: AppLocalizations.of(context)!.passwordChangedText,
          actionButtonText: 'close',
        );
        AppNavigation.previousScreen(context);
      } else if (newPassword != null && response?.done == false && newPassword != '') {
        print("is password changed =====>${response?.done}");
        customSnackBar(
          context: navigatorKey.currentContext!,
          message: response!.result[0].toString(),
          actionButtonText: 'close',
        );
        AppNavigation.previousScreen(context);
      } else {
        customSnackBar(
          context: navigatorKey.currentContext!,
          message: AppLocalizations.of(context)!.errorText,
          actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase(),
        );
        AppNavigation.previousScreen(context);
      }

      // customSnackBar(
      //   context: context,
      //   message: "Done",
      //   actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase(),
      // );
    }
  }

  Future<void> profileDataApiCall({required BuildContext context}) async {
    emit(true);

    if (!isApiProfileCalled) {
      print("api started =====>");
      final ProfileDataApiRepository personDataApiRepository = ProfileDataApiRepository();
      final response = await personDataApiRepository.profileDataApi(context: context);
      print("api done =====>${response}");

      if (response?.statusCode == 200 && response?.addresses?.length != 0) {
        isApiProfileCalled = true;
        // appDB.user?.profileData = response;
        // appDB.user?.profileData?.isApiCallDone = true;
        // appDB.user?.save();

        // ProfileData? data = appDB.user?.profileData;
        // data?.isApiCallDone = true;
        // appDB.user?.profileData = data;

        response?.isApiCallDone = true;
        final user = appDB.user;
        user?.profileData = response;
        appDB.user = user;
        appDB.user?.save();
      } else {
        // customSnackBar(context: context, message: AppLocalizations.of(context)!.errorText,actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
      }
    }
    emit(false);
  }

  Future<void> getProfileDataApi(BuildContext context) async {
    print("--------------------->${appDB.user?.PhotoId}");

    final cachedImageData = prefs.getString(AppConstants.cachedImageData);
    final cachedPhotoId = prefs.getString(AppConstants.cachedPhotoId);

    if (cachedImageData != null && cachedPhotoId == appDB.user?.PhotoId) {
      final Uint8List bytes = base64.decode(cachedImageData);
      getImage = Image.memory(bytes);
      print("Using cached image data: ${getImage?.image}");
      isImageLoading.value = false;
    } else {
      isImageLoading.value = true;

      final response1 = await profileImageApiRepository.getProfilePicApi(
        context: context,
        guid: appDB.user?.PhotoId ?? '',
      );

      isImageLoading.value = false;

      if (response1?.fileContent != null) {
        final encodedStr = response1?.fileContent;
        Uint8List bytes = base64.decode(encodedStr!);
        getImage = Image.memory(bytes);

        await prefs.setString(AppConstants.cachedImageData, encodedStr);
        await prefs.setString(AppConstants.cachedPhotoId, appDB.user!.PhotoId.toString());

        print("Fetched and cached image data: ${getImage?.image}");
      }
    }
  }

  Future<String> memoryImageToString(MemoryImage memoryImage) async {
    final codec = await ui.instantiateImageCodec(memoryImage.bytes);
    final frame = await codec.getNextFrame();
    final byteData = await frame.image.toByteData(format: ui.ImageByteFormat.png);

    if (byteData != null) {
      final buffer = byteData.buffer.asUint8List();
      final encodedString = base64Encode(buffer);
      return encodedString;
    } else {
      throw Exception("Failed to convert MemoryImage to string");
    }
  }
}

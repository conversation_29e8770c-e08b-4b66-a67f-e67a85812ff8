import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ionicons/ionicons.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/common_button.dart';
import 'package:staff_medewerker/common/custom_widgets/common_snackbar.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/image_picker_bottomsheet.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/pin_module/common_show_dialog.dart';
import 'package:staff_medewerker/screens/profile_module/bloc/profile_screen_cubit.dart';
import 'package:staff_medewerker/screens/profile_module/ui/address_info.dart';
import 'package:staff_medewerker/screens/profile_module/ui/contact_info.dart';
import 'package:staff_medewerker/screens/profile_module/ui/personal_info.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

import '../../../common/custom_widgets/common_profile_picture.dart';
import '../../../common/custom_widgets/shimmer_effect.dart';
import '../change_password_module/ui/change_password_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    print("--------------------->${appDB.user?.PhotoId}");

    final profileBloc = BlocProvider.of<ProfileCubit>(context);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      await profileBloc.getProfileDataApi(context);
      setState(() {});
    });

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      await context.read<ProfileCubit>().profileDataApiCall(context: context);
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProfileCubit, bool>(
      builder: (ctx, state) {
        print("BlocBuilder rebuilt with state: $state");
        final imageData = ctx.read<ProfileCubit>();
        return Scaffold(
          appBar: CustomAppBar(title: AppLocalizations.of(context)!.profileText),
          body: Padding(
            padding: EdgeInsets.all(AppSize.sp25),
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SpaceV(
                    AppSize.h50,
                  ),
                  GestureDetector(
                    onTap: () async {
                      imagePickerBottomSheet(
                          context: context,
                          onCameraPress: () async {
                            askPermission(context, Permission.camera, () {
                              imageData.pickImage(context: context, profileImage: ImageSource.camera);
                              print("image  =====>${ImageSource.camera}");
                              print("image  =====>${imageData.getImage!.image}");
                            });
                          },
                          onGalleryPress: () async {
                            askPermission(context, Permission.storage, () {
                              imageData.pickImage(context: context, profileImage: ImageSource.gallery);
                              print("image  =====>${ImageSource.gallery}");
                              print("image  =====>${imageData.getImage!.image}");
                            });
                          });
                    },
                    child: Stack(
                      alignment: Alignment.bottomRight,
                      children: [
                        (imageData.getImage?.image != '' && imageData.getImage?.image != null)
                            ? ValueListenableBuilder(
                                valueListenable: imageData.isImageLoading,
                                builder: (context, value, child) {
                                  if (!imageData.isImageLoading.value) {
                                    return Container(
                                      height: AppSize.w140,
                                      width: AppSize.w140,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        image: DecorationImage(
                                          image: imageData.getImage!.image,
                                          fit: BoxFit.cover, // You can adjust the fit as needed
                                        ),
                                      ),
                                    );
                                  } else {
                                    return ShimmerWidget(
                                      // baseColor: AppColors.red,
                                      // highlightColor: AppColors.primaryColor,
                                      child: Container(
                                        height: AppSize.w140,
                                        width: AppSize.w140,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: AppColors.white,
                                        ),
                                      ),
                                    );
                                  }
                                },
                              )
                            : ValueListenableBuilder(
                                valueListenable: imageData.imagePath,
                                builder: (context, imagePath, child) {
                                  print("image date =======>${imageData.imageString}");
                                  return CommonCircularProfilePicture(
                                    url: '',
                                    height: AppSize.w140,
                                    width: AppSize.w140,
                                    localUrl: imagePath.isEmpty ? null : imagePath,
                                  );
                                },
                              ),
                        Padding(
                          padding: EdgeInsets.only(bottom: AppSize.h8, right: AppSize.w6),
                          child: CircleAvatar(
                              backgroundColor: context.themeColors.mediumBlackColor,
                              child: Icon(
                                Ionicons.camera,
                                color: context.themeColors.textColor,
                              )),
                        ),
                      ],
                    ),
                  ),
                  Text(
                    "${appDB.user?.UserNamePerson}",
                    style: context.textTheme.headlineLarge!
                        .copyWith(color: context.themeColors.textColor, fontSize: AppSize.sp18),
                  ),
                  Text(
                    '${appDB.user?.LicenseName}',
                    style: context.textTheme.headlineLarge!.copyWith(
                        color: context.themeColors.textColor, fontSize: AppSize.sp15, fontWeight: FontWeight.normal),
                  ),
                  SpaceV(
                    AppSize.h50,
                  ),
                  ReusableContainerButton(
                    isTrailingIcon: Ionicons.chevron_forward_sharp,
                    onPressed: () {
                      AppNavigation.nextScreen(context, const PersonalInfoScreen());
                    },
                    buttonText: AppLocalizations.of(context)!.personalInformationText,
                    textStyle: context.textTheme.bodySmall!
                        .copyWith(color: AppColors.white, fontWeight: FontWeight.normal, fontSize: AppSize.sp14),
                  ),
                  SpaceV(
                    AppSize.h10,
                  ),
                  ReusableContainerButton(
                    isTrailingIcon: Ionicons.chevron_forward_sharp,
                    onPressed: () {
                      AppNavigation.nextScreen(context, const ContactInfoScreen());
                    },
                    buttonText: AppLocalizations.of(context)!.contactInformationText,
                    textStyle: context.textTheme.bodySmall!
                        .copyWith(color: AppColors.white, fontWeight: FontWeight.normal, fontSize: AppSize.sp14),
                  ),
                  SpaceV(
                    AppSize.h10,
                  ),
                  ReusableContainerButton(
                    isTrailingIcon: Ionicons.chevron_forward_sharp,
                    onPressed: () {
                      AppNavigation.nextScreen(context, const AddressInfoScreen());
                    },
                    buttonText: AppLocalizations.of(context)!.addressInformationText,
                    textStyle: context.textTheme.bodySmall!
                        .copyWith(color: AppColors.white, fontWeight: FontWeight.normal, fontSize: AppSize.sp14),
                  ),
                  SpaceV(
                    AppSize.h10,
                  ),
                  ReusableContainerButton(
                    isTrailingIcon: Ionicons.chevron_forward_sharp,
                    onPressed: () {
                      AppNavigation.nextScreen(context, const ChangePasswordScreen());
                    },
                    buttonText: AppLocalizations.of(context)!.changePasswordText,
                    textStyle: context.textTheme.bodySmall!
                        .copyWith(color: AppColors.white, fontWeight: FontWeight.normal, fontSize: AppSize.sp14),
                  ),
                  SpaceV(
                    AppSize.h10,
                  ),
                  ReusableContainerButton(
                    isTrailingIcon: Ionicons.chevron_forward_sharp,
                    onPressed: () {
                      showDialog(
                        context: context,
                        builder: (dialogContext) {
                          return CustomAlertDialog(
                            context: context,
                            title: AppLocalizations.of(context)!.deleteAccountText,
                            message: AppLocalizations.of(context)!.deleteAccountDetailsText,
                            onOkPressed: () {
                              Navigator.pop(dialogContext);
                            },
                          );
                          // return AlertDialog(
                          //   content: SizedBox(
                          //     width: MediaQuery.of(context).size.width,
                          //     child: Text(
                          //       AppLocalizations.of(context)!.deleteAccountDetailsText,
                          //       style: context.textTheme.bodyMedium?.copyWith(
                          //         fontSize: AppSize.sp15,
                          //         fontWeight: FontWeight.normal,
                          //         color: context.themeColors.darkGreyColor,
                          //       ),
                          //     ),
                          //   ),
                          //   actions: [
                          //     TextButton(
                          //       onPressed: () {
                          //         Navigator.pop(dialogContext);
                          //       },
                          //       child: Text(
                          //         "OK".toUpperCase(),
                          //         style: context.textTheme.bodyLarge?.copyWith(
                          //           fontSize: AppSize.sp12,
                          //           color: context.themeColors.primaryColor,
                          //         ),
                          //       ),
                          //     )
                          //   ],
                          // );
                        },
                      );
                    },
                    buttonText: AppLocalizations.of(context)!.deleteAccountText,
                    textStyle: context.textTheme.bodySmall!
                        .copyWith(color: AppColors.white, fontWeight: FontWeight.normal, fontSize: AppSize.sp14),
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // for asking permission for camera and storage.
  static Future<void> askPermission(BuildContext context, Permission permission, VoidCallback onGranted) async {
    var permissionStatus = await permission.request();

    if (permissionStatus == PermissionStatus.permanentlyDenied) {
      if (permission == Permission.camera) {}
      customSnackBar(
          context: context,
          message: permission == Permission.camera
              ? "You need permission for capture image"
              : "You need permission for choose an image",
          actionButtonText: "Give Permission",
          onPressed: () async {
            try {
              await openAppSettings();
            } catch (e) {
              print(e);
            }
          });
    } else if (permissionStatus != PermissionStatus.denied) {
      onGranted();
    }
  }
}

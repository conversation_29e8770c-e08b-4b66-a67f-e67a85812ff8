// import 'dart:io';
//
// import 'package:flutter/material.dart';
// import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
// import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
// import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
// import 'package:staff_medewerker/utils/appsize.dart';
// import 'package:flutter_gen/gen_l10n/app_localizations.dart';
//
// class ThirdPartySoftwareScreen extends StatelessWidget {
//   const ThirdPartySoftwareScreen({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: CustomAppBar(title: AppLocalizations.of(context)!.thirdPartySoftwareLabel),
//       body: Padding(
//           padding: EdgeInsets.symmetric(vertical: AppSize.sp27, horizontal: AppSize.sp14),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Text(
//                 AppLocalizations.of(context)!.generalInformationText,
//                 style: context.textTheme.bodyMedium?.copyWith(
//                   fontSize: AppSize.sp12,
//                 ),
//               ),
//               SpaceV(AppSize.h6),
//               Divider(thickness: 1,
//                 color: context.themeColors.greyColor,
//               ),
//               SpaceV(AppSize.h6),
//
//               Text(
//                 "Ionic2-Calender",
//                 style: context.textTheme.bodyLarge?.copyWith(
//                   fontSize: AppSize.sp16,fontWeight: FontWeight.w700
//                 ),
//               ),
//               SpaceV(AppSize.h14),
//               Text(
//                 AppLocalizations.of(context)!.licenseText,
//                 style: context.textTheme.bodyMedium?.copyWith(
//                   fontSize: AppSize.sp12,
//                 ),
//               ),
//               SpaceV(AppSize.h6),
//               Divider(thickness: 1,
//                 color: context.themeColors.greyColor,
//               ),
//             ],
//           )),
//     );
//   }
// }

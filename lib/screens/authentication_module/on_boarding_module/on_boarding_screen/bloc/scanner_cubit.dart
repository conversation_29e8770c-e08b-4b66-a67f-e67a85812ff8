import 'package:bloc/bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/service/api_service/server_constants.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';

import '../../../../../common/custom_widgets/common_snackbar.dart';
import '../../../../../common/custom_widgets/custom_loader.dart';
import '../../../../../common/functions/shared_prefs.dart';
import '../../../../../utils/constant/constant.dart';
import '../../../login_module/ui/login_screen.dart';
import '../on_boarding_repository/scanner_api_repository.dart';

part 'scanner_state.dart';

class ScannerCubit extends Cubit<ScannerInitial> {
  ScannerCubit() : super(ScannerInitial());
  late QRViewController controller;
  ValueNotifier<String> result = ValueNotifier('');
  ValueNotifier<bool> inProgress = ValueNotifier(false);
  final ScannerApiRepository scannerApiRepository = ScannerApiRepository();

  void updateQrController({QRViewController? controller}) {
    controller = controller;
    emit(ScannerInitial());
  }

  Future<void> validateActivationLink(BuildContext context, String? skinId) async {
    await Loader.showLoaderDialog(navigatorKey.currentContext!);
    Uri uri = Uri.parse(skinId ?? '');

    final skinIdPattern = RegExp(r'SkinId=([a-zA-Z0-9-]+)');
    final match = skinIdPattern.firstMatch(skinId!);

    skinId = match?.group(1);
    ServerConstant.base_url = '${uri.scheme}://${uri.host}${uri.path}/';
    await prefs.setString('base_url', ServerConstant.base_url);
    ServerConstant.base_url = prefs.getString('base_url') ?? '';
    print("skinIdPattern =====>${ServerConstant.base_url}");
    if (skinId != null && RegExp(r'[a-zA-Z0-9-]+').hasMatch(skinId)) {
      final response = await scannerApiRepository.scannerApi(context: context, skinDateTime: skinId);
      print("api done =====>$response");
      print("api skinId =====>$skinId");
      print("api skinId =====>$skinId");
      Loader.closeLoadingDialog(navigatorKey.currentContext!);
      inProgress.value = false;
      if (response == 200) {
        // customSnackBar(
        //   context: navigatorKey.currentContext!,
        //   message: 'Verification Done',
        //   actionButtonText: 'close',
        // );
        Prefs.preferences.remove(AppConstants.savedPinNumber);
        AppNavigation.nextScreen(
            navigatorKey.currentContext!,
            LoginScreen(
              isFromScannerScreen: true,
            ));
      } else {
        customSnackBar(
          context: navigatorKey.currentContext!,
          message: AppLocalizations.of(navigatorKey.currentContext!)!.errorText,
          actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!.closeText.toUpperCase(),
        );
      }
    } else {
      await Future.delayed(Duration(milliseconds: 100));
      Loader.closeLoadingDialog(navigatorKey.currentContext!);
      customSnackBar(
        context: navigatorKey.currentContext!,
        message: AppLocalizations.of(navigatorKey.currentContext!)!.invalidLink,
        actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!.closeText.toUpperCase(),
      );
    }
  }

  Future<void> validateActivationLinkApiForQRCode(BuildContext context) async {
    String skinId = result.value;

    Loader.showLoaderDialog(navigatorKey.currentContext!);
    Uri uri = Uri.parse(skinId);
    print("uri result code: =====> ${uri}");
    skinId = uri.queryParameters["SkinId"] ?? '';
    print("SkinId: =====> $skinId");
    print("result code: =====> ${skinId}");
    print("result code: =====> ${result.value}");
    final skinIdPattern = RegExp(r'[a-zA-Z0-9-]+'); // Modify the pattern as needed

    ServerConstant.base_url = '${uri.scheme}://${uri.host}${uri.path}/';
    await prefs.setString('base_url', ServerConstant.base_url);
    ServerConstant.base_url = prefs.getString('base_url') ?? '';
    print("result code: =====> ${ServerConstant.base_url}");

    if (skinId.isNotEmpty && skinIdPattern.hasMatch(skinId)) {
      final response = await scannerApiRepository.scannerApi(context: context, skinDateTime: skinId);
      Loader.closeLoadingDialog(navigatorKey.currentContext!);
      if (skinId.isNotEmpty && response == 200) {
        print("api  =====>");

        Prefs.preferences.remove(AppConstants.savedPinNumber);

        AppNavigation.nextScreen(
            navigatorKey.currentContext!,
            LoginScreen(
              isFromScannerScreen: true,
            ));
      } else {
        customSnackBar(
          context: navigatorKey.currentContext!,
          message: AppLocalizations.of(navigatorKey.currentContext!)!.errorText,
          actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!.closeText.toUpperCase(),
        );
      }
    } else {
      await Future.delayed(Duration(milliseconds: 300));
      Loader.closeLoadingDialog(navigatorKey.currentContext!);

      customSnackBar(
        context: navigatorKey.currentContext!,
        message: AppLocalizations.of(navigatorKey.currentContext!)!.invalidLink,
        actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!.closeText.toUpperCase(),
      );
    }
  }
}

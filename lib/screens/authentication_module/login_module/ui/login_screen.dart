import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/authentication_module/forget_password_module/ui/forget_password_screen.dart';
import 'package:staff_medewerker/screens/authentication_module/login_module/bloc/login_cubit.dart';
import 'package:staff_medewerker/screens/authentication_module/login_module/widget/common_textfield.dart';
import 'package:staff_medewerker/screens/authentication_module/on_boarding_module/on_boarding_screen/ui/onboarding_screens.dart';
import 'package:staff_medewerker/screens/authentication_module/widget/common_button.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/asset_path/assets_path.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';
import 'package:staff_medewerker/utils/constant/constant.dart';

class LoginScreen extends StatefulWidget {
  bool isFromPinScreen;
  final bool isFromScannerScreen;
  LoginScreen({Key? key, this.isFromPinScreen = false, this.isFromScannerScreen = false}) : super(key: key);

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final TextEditingController userNameController = TextEditingController();

  final TextEditingController passwordController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      if (!widget.isFromPinScreen) {
        // await PermissionProvider.checkForPermission();
        await prefs.setString(AppConstants.lastPosition, AppConstants.loginScreen);
        print("============>${prefs.setString(AppConstants.lastPosition, AppConstants.loginScreen)}");
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    print('LoginScreen ssDevice ID: $deviceId');

    return Scaffold(
      backgroundColor: AppColors.primaryColor,
      resizeToAvoidBottomInset: false,
      appBar: widget.isFromPinScreen ? AppBar(backgroundColor: Colors.transparent, elevation: 0) : null,
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: widget.isFromPinScreen ? AppSize.w16 : AppSize.w30),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: widget.isFromPinScreen ? MainAxisAlignment.start : MainAxisAlignment.center,
            children: [
              widget.isFromPinScreen ? SpaceV(AppSize.h62) : SpaceV(AppSize.h160),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: AppSize.w30),
                child: widget.isFromPinScreen
                    ? Image.asset(
                        AssetsPath.mainWhiteLogo,
                        //  height: AppSize.h70,
                      )
                    : Image.asset(
                        AssetsPath.mainWhiteLogo1,
                        //  height: AppSize.h125,
                      ),
              ),
              SpaceV(widget.isFromPinScreen ? AppSize.h60 : AppSize.h60),
              widget.isFromPinScreen
                  ? Padding(
                      padding: EdgeInsets.only(bottom: AppSize.h20),
                      child: Text(
                        AppLocalizations.of(context)!.forgotPinInfoText,
                        textAlign: TextAlign.start,
                        style: context.textTheme.titleMedium?.copyWith(color: AppColors.white, fontSize: AppSize.sp14),
                      ),
                    )
                  : Container(),
              CommonTextField(
                hintText: AppLocalizations.of(context)!.userName,
                controller: userNameController,
              ),
              SpaceV(AppSize.h10),
              CommonTextField(
                isSuffixIcon: true,
                hintText: AppLocalizations.of(context)!.passWord,
                controller: passwordController,
              ),
              SpaceV(AppSize.h10),
              CommonButton(
                onPressed: () {
                  final loginBloc = BlocProvider.of<LoginCubit>(navigatorKey.currentContext!, listen: false);
                  loginBloc.loginApi(context, userNameController.text.trim(), passwordController.text.trim(), deviceId,
                      widget.isFromPinScreen, widget.isFromScannerScreen);
                  print("devvvvvvvvvviceId:${deviceId}");
                },
                title: widget.isFromPinScreen
                    ? AppLocalizations.of(context)!.resetPinText.toUpperCase()
                    : AppLocalizations.of(context)!.signIn.toUpperCase(),
                borderRadius: 0,
                height: widget.isFromPinScreen ? AppSize.h40 : AppSize.h32,
                buttonColor: AppColors.darkGreenColor,
                titleStyle: context.textTheme.titleSmall?.copyWith(
                  color: AppColors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SpaceV(AppSize.h10),
              widget.isFromPinScreen
                  ? Container()
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        GestureDetector(
                            onTap: () {
                              AppNavigation.nextScreen(context, OnBoardingScreen());
                            },
                            child: Text(
                              AppLocalizations.of(context)!.scanNewCode,
                              textAlign: TextAlign.center,
                              style: context.textTheme.titleMedium
                                  ?.copyWith(color: AppColors.white, fontSize: AppSize.sp14),
                            )),
                        GestureDetector(
                            onTap: () {
                              AppNavigation.nextScreen(context, ForgetPasswordScreen());
                            },
                            child: Text(
                              AppLocalizations.of(context)!.forgetPassword,
                              textAlign: TextAlign.center,
                              style: context.textTheme.titleMedium
                                  ?.copyWith(color: AppColors.white, fontSize: AppSize.sp14),
                            )),
                      ],
                    )
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/common_functions/common_dateformat_function.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/common_button.dart';
import 'package:staff_medewerker/common/custom_widgets/common_snackbar.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_textfield.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/absence_module/bloc/absence_screen_cubit.dart';
import 'package:staff_medewerker/screens/absence_module/ui/request_leave_screen/bloc/request_leave_screen_cubit.dart';
import 'package:staff_medewerker/screens/absence_module/ui/request_leave_screen/common/common_dialog_radiobutton.dart';
import 'package:staff_medewerker/screens/absence_module/ui/request_leave_screen/select_date_range_screen.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

class RequestLeaveScreen extends StatefulWidget {
  @override
  State<RequestLeaveScreen> createState() => _RequestLeaveScreenState();
}

class _RequestLeaveScreenState extends State<RequestLeaveScreen> {
  final absenceBloc = BlocProvider.of<AbsenceCubit>(navigatorKey.currentContext!);
  String selectedLeaveType = AppLocalizations.of(navigatorKey.currentContext!)!.selectLeaveTypeText;
  String selectedLeaveTypeGuid = "";

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      final requestLeaveBloc = BlocProvider.of<RequestLeaveCubit>(context, listen: false);
      requestLeaveBloc.clearFormData(context: context);
      await absenceBloc.leaveTypeApiCall(context: context);
    });
  }

  @override
  Widget build(BuildContext context) {
    final requestLeaveBloc = BlocProvider.of<RequestLeaveCubit>(context, listen: false);
    return Scaffold(
      backgroundColor: context.themeColors.homeContainerColor,
      appBar: CustomAppBar(title: AppLocalizations.of(context)!.requestLeaveText),
      body: Column(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: AppSize.sp10),
            child: Column(
              children: [
                SpaceV(AppSize.h10),
                ListTile(
                  dense: true,
                  shape: Border(
                    bottom: BorderSide(width: 0, color: context.themeColors.greyColor),
                  ),
                  title: Padding(
                    padding: EdgeInsets.only(bottom: AppSize.h4),
                    child: Text(AppLocalizations.of(context)!.periodText,
                        style: context.textTheme.bodySmall
                            ?.copyWith(color: context.themeColors.textColor, fontSize: AppSize.sp11)),
                  ),
                  subtitle: ValueListenableBuilder(
                    builder: (BuildContext context, value, Widget? child) {
                      print('value =====>$value');

                      return Text(value,
                          style: context.textTheme.bodyMedium
                              ?.copyWith(color: context.themeColors.textColor, fontSize: AppSize.sp14));
                    },
                    valueListenable: requestLeaveBloc.selectedDateRange,
                  ),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(
                          top: AppSize.h14,
                          right: AppSize.h14,
                        ),
                        child: Icon(
                          Ionicons.calendar,
                          color: context.themeColors.primaryColor,
                        ),
                      ),
                    ],
                  ),
                  onTap: () {
                    AppNavigation.nextScreen(context, SelectDateRangeScreen());
                  },
                ),
                ListTile(
                  dense: true,
                  shape: Border(
                    bottom: BorderSide(width: 0, color: context.themeColors.greyColor),
                  ),
                  title: Padding(
                    padding: EdgeInsets.only(bottom: AppSize.h4),
                    child: Text(AppLocalizations.of(context)!.leaveTypeText,
                        style: context.textTheme.bodySmall
                            ?.copyWith(color: context.themeColors.textColor, fontSize: AppSize.sp11)),
                  ),
                  subtitle: Text(selectedLeaveType,
                      style: context.textTheme.bodyMedium?.copyWith(
                          color: selectedLeaveType == AppLocalizations.of(context)!.selectLeaveTypeText
                              ? context.themeColors.greyColor
                              : context.themeColors.textColor,
                          fontSize: AppSize.sp14)),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(
                          top: AppSize.h14,
                          right: AppSize.h14,
                        ),
                        child: Icon(
                          Icons.arrow_drop_down,
                          color: context.themeColors.greyColor,
                        ),
                      ),
                    ],
                  ),
                  onTap: () {
                    showDialog(
                      context: context,
                      builder: (context) {
                        List<String> leaveTypeList = [];
                        absenceBloc.leaveTypeList.forEach((element) {
                          leaveTypeList.add(element.title);
                        });

                        print(absenceBloc.leaveTypeList);

                        return CustomRadioButtonDialog(
                          dialogTitle: AppLocalizations.of(context)!.leaveTypeText,
                          list: leaveTypeList,
                          onOKPressed: (selectedTitle) {
                            // Find the leave type with the selected title
                            var selectedLeaveTypeData =
                                absenceBloc.leaveTypeList.firstWhere((element) => element.title == selectedTitle);
                            selectedLeaveType = selectedTitle;
                            setState(() {
                              selectedLeaveTypeGuid = selectedLeaveTypeData.guid;
                            });
                            print("Selected Leave Type Title: $selectedTitle");
                            print("Selected Leave Type Guid: $selectedLeaveTypeGuid");
                          },
                          selectedValue: selectedLeaveType,
                        );
                      },
                    );
                    // showDialog(
                    //   context: context,
                    //   builder: (context) {
                    //     List<String> leaveTypeList = [];
                    //     absenceBloc.leaveTypeList.forEach((element) {
                    //       leaveTypeList.add(element.title);
                    //     });
                    //     absenceBloc.leaveTypeList.forEach((element) {
                    //       leaveTypeList.add(element.guid);
                    //     });
                    //
                    //     print(absenceBloc.leaveTypeList);
                    //
                    //     return CustomRadioButtonDialog(
                    //       dialogTitle: AppLocalizations.of(context)!.leaveTypeText,
                    //       list: leaveTypeList,
                    //       onOKPressed: (value) {
                    //         setState(() {
                    //           selectedLeaveType = value;
                    //           selectedLeaveType = value;
                    //         });
                    //         print(value);
                    //       },
                    //       selectedValue: selectedLeaveType,
                    //     );
                    //   },
                    // );
                  },
                ),
                SpaceV(AppSize.h14),
                ValueListenableBuilder(
                  valueListenable: requestLeaveBloc.workedHoursController,
                  builder: (BuildContext context, TextEditingController value, Widget? child) {
                    return CustomTextField(
                      controller: value,
                      hintText: "",
                      labelText: AppLocalizations.of(context)!.workedHoursText,
                      focusedBorder: context.themeColors.buttonRedColor,
                      border: UnderlineInputBorder(
                        borderSide: BorderSide(color: context.themeColors.greyColor, width: 0.4),
                      ),
                    );
                  },
                ),
                SpaceV(AppSize.h14),
                CustomTextField(
                    maxLines: 4,
                    controller: requestLeaveBloc.leaveReasonController.value,
                    hintText: AppLocalizations.of(context)!.leaveReasonText,
                    border: InputBorder.none),
                SpaceV(AppSize.h14),
                ValueListenableBuilder(
                  valueListenable: requestLeaveBloc.isLoading,
                  builder: (context, value, child) {
                    return ReusableContainerButton(
                        isLoading: requestLeaveBloc.isLoading.value,
                        height: AppSize.h32,
                        onPressed: () async {
                          if (requestLeaveBloc.selectedDateRange == AppLocalizations.of(context)!.selectPeriodText ||
                              requestLeaveBloc.workedHoursController.value.text == "" ||
                              selectedLeaveType == AppLocalizations.of(context)!.selectLeaveTypeText ||
                              requestLeaveBloc.leaveReasonController.value.text == "") {
                            customSnackBar(
                                context: context,
                                message: AppLocalizations.of(context)!.someFieldsEmptyText,
                                actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
                          } else {
                            DateTime startDate = requestLeaveBloc.selectedRanges.value.startDate ?? DateTime.now();
                            DateTime endDate = requestLeaveBloc.selectedRanges.value.endDate ?? DateTime.now();

                            await requestLeaveBloc.submitLeaveApiCall(
                                context: context,
                                startDate:
                                    "${startDate.year}${DateFormatFunctions.formatDay(startDate.month)}${DateFormatFunctions.formatDay(startDate.day)}",
                                endDate:
                                    "${endDate.year}${DateFormatFunctions.formatDay(endDate.month)}${DateFormatFunctions.formatDay(endDate.day)}",
                                selectedLeaveTypeGuid: selectedLeaveTypeGuid,
                                workedHours: requestLeaveBloc.workedHoursController.value.text,
                                leaveReason: requestLeaveBloc.leaveReasonController.value.text);
                          }
                        },
                        buttonText: AppLocalizations.of(context)!.submitLeaveText.toUpperCase(),
                        borderRadius: BorderRadius.zero,
                        textStyle:
                            context.textTheme.bodyLarge?.copyWith(color: AppColors.white, fontSize: AppSize.sp14));
                  },
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:staff_medewerker/screens/balance_screen/repository/my_balances_overview/balance_overview_model.dart';
import 'package:staff_medewerker/screens/balance_screen/repository/my_balances_overview/my_balances_overview_api_repository.dart';
import 'package:staff_medewerker/screens/absence_module/repository/timesheet_leave_InYear/timesheet_leave_model.dart';
import 'package:staff_medewerker/screens/absence_module/repository/timesheet_leave_InYear/timesheet_leave_InYear_api_repository.dart';
import 'package:staff_medewerker/screens/absence_module/ui/request_leave_screen/leave_types_repository/leave_types_repository.dart';
import 'package:staff_medewerker/screens/absence_module/ui/request_leave_screen/leave_types_repository/leave_type_model.dart';

class AbsenceCubit extends Cubit<bool> {
  AbsenceCubit() : super(false);
  ValueNotifier<bool> isLoading = ValueNotifier(false);
  List<MyBalancesOverviewModel> myBalancesOverviewList = [];
  List<LeaveTypeModel> leaveTypeList = [];
  ValueNotifier<List<MyBalancesOverviewModel>> filteredList = ValueNotifier([]);
  ValueNotifier<List<MyBalancesOverviewModel>> totalList = ValueNotifier([]);
  String selectedDate = "";
  ValueNotifier<String> selectedDateString = ValueNotifier('${formatDate(DateTime.now())}');
  ValueNotifier<MyBalancesOverviewModel> myBalancesOverview =
      ValueNotifier(MyBalancesOverviewModel(sequence: 0, categorie: "", total: false, vak: "", atv: "", totaal: ""));

  List<TimeSheetLeaveInYearModel> timesheetLeaveInYearList = [];

  Future<void> myBalancesOverviewApiCall(
      {required BuildContext context, required String selectedDate, bool isFirstTime = false}) async {
    log('isFirstTime ------->${isFirstTime}');
    if (isFirstTime) {
      isLoading.value = true;
    }

    final MyBalancesOverviewRepository myBalancesOverviewRepository = MyBalancesOverviewRepository();
    final response =
        await myBalancesOverviewRepository.myBalancesOverviewApi(context: context, selectedDate: selectedDate);

    if (response!.isNotEmpty) {
      myBalancesOverviewList.clear();
      myBalancesOverviewList.addAll(response);
      for (var element in myBalancesOverviewList) {
        if (element.sequence == 5) {
          myBalancesOverview.value = element;
          break;
        }
      }
    } else {
      // customSnackBar(context: context, message: AppLocalizations.of(context)!.errorText,actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
    }
    if (isFirstTime) {
      isLoading.value = false;
    }
    myBalancesOverview.notifyListeners();
    emit(false);
  }

  Future<void> timesheetLeaveInYearApiCall({required BuildContext context, required String selectedYear}) async {
    isLoading.value = true;
    print("api started =====>");
    final TimesheetLeaveInYearApiRepository timesheetLeaveInYearApiRepository = TimesheetLeaveInYearApiRepository();
    final response =
        await timesheetLeaveInYearApiRepository.timesheetLeaveInYearApi(context: context, selectedYear: selectedYear);

    if (response!.isNotEmpty) {
      timesheetLeaveInYearList.clear();
      timesheetLeaveInYearList.addAll(response);
    } else {
      // customSnackBar(context: context, message: AppLocalizations.of(context)!.errorText,actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
    }
    isLoading.value = false;
    emit(false);
  }

  Future<void> leaveTypeApiCall({required BuildContext context}) async {
    isLoading.value = true;
    print("leaveTypeApiCall =====>");
    final LeaveTypeApiRepository leaveTypeApiRepository = LeaveTypeApiRepository();
    final response = await leaveTypeApiRepository.leaveTypeApi(
      context: context,
    );

    if (response!.isNotEmpty) {
      leaveTypeList.clear();
      leaveTypeList.addAll(response);
    } else {
      // customSnackBar(context: context, message: AppLocalizations.of(context)!.errorText,actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
    }
    isLoading.value = false;
    emit(false);
  }

  void setDate({required DateTime selectedValue}) {
    selectedDateString.value = dateToString(selectedValue);
  }

  static formatDate(DateTime date) {
    final formatter = DateFormat('dd MMMM yyyy');
    return formatter.format(date);
  }

  String dateToString(DateTime date) {
    final formatter = DateFormat('dd MMMM yyyy');
    return formatter.format(date);
  }

  DateTime stringToDate(String dateString) {
    final formatter = DateFormat('dd MMMM yyyy');
    return formatter.parse(dateString);
  }
}

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'package:ionicons/ionicons.dart';
import 'package:isoweek/isoweek.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/common/functions/shared_prefs.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/home_module/bloc/home_cubit.dart';
import 'package:staff_medewerker/screens/home_module/widget/home_shift_shimmer.dart';
import 'package:staff_medewerker/screens/home_module/widget/shift_shimmer.dart';
import 'package:staff_medewerker/screens/news_module/bloc/news_screen_cubit.dart';
import 'package:staff_medewerker/screens/news_module/news_auto_open_screen.dart';
import 'package:staff_medewerker/screens/notification_module/bloc/notification_cubit.dart';
import 'package:staff_medewerker/screens/pin_module/common_show_dialog.dart';
import 'package:staff_medewerker/screens/pin_module/pin_screen.dart';
import 'package:staff_medewerker/screens/schedule_module/bloc/schedule_cubit.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/constant/constant.dart';

import '../../../common/common_functions/common_dateformat_function.dart';
import '../../../utils/asset_path/assets_path.dart';
import '../../../utils/colors/app_colors.dart';
import '../../hours_module/bloc/hours_cubit.dart';
import '../../news_module/news_screen.dart';
import '../widget/home_news_container.dart';

class HomeScreen extends StatefulWidget {

  final bool? isApiCall;
  final bool? isFromFirstTime;
  const HomeScreen({Key? key, this.isFromFirstTime = false,this.isApiCall}) : super(key: key);

  static ScrollController scrollController = ScrollController();
  static ItemScrollController itemScrollController = ItemScrollController();

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final newsBloc = BlocProvider.of<NewsCubit>(navigatorKey.currentContext!);

  bool firstTimeNewScreenNavigate = true;

  @override
  void initState() {
    super.initState();
    final newsBloc = BlocProvider.of<NewsCubit>(context);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      if(widget.isApiCall??true){
        await newsBloc.newsAutoOpenApiCall(context: context).then((value) {
          if (newsBloc.newsAutoOpenList.length != 0) {
            AppNavigation.nextScreen(navigatorKey.currentContext!, NewsAutoOpenScreen());
          }
        }).then(
              (value) async {
            final homeBloc = BlocProvider.of<HomeCubit>(context);
            final notificationBloc = BlocProvider.of<NotificationCubit>(context);
            if (widget.isFromFirstTime == true) {
              Prefs.preferences.remove(AppConstants.savedPinNumber);
              showDialog(
                context: context,
                builder: (dialogContext) {
                  return CustomAlertDialog(
                    context: context,
                    title: AppLocalizations.of(context)!.setPinCode,
                    message: AppLocalizations.of(context)!.setPinMsg,
                    isCancelButton: true,
                    cancelText: AppLocalizations.of(context)!.noThanks,
                    okButtonText: AppLocalizations.of(context)!.setPin,
                    onOkPressed: () {
                      AppNavigation.nextScreen(
                          dialogContext,
                          EnterPinPage(
                            isRemovePinScreen: false,
                            isNavigateFromHomeScreen: false,
                            isFromHomeScreen: true,
                          ));
                    },
                    cancelPressed: () {
                      Navigator.pop(dialogContext);
                    },
                  );
                },
              );
            }
            await refreshApiData(context);
            notificationBloc.fetchNotificationList(context: context);
          },
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final scheduleBloc = BlocProvider.of<ScheduleCubit>(context);
    final hourBloc = BlocProvider.of<HoursCubit>(context);
    final homeBloc = BlocProvider.of<HomeCubit>(context);
    return Scaffold(
      backgroundColor: context.themeColors.homeBackgroundColor,
      appBar: AppBar(
        
        title: Image.asset(AssetsPath.mainWhiteLogo, height: AppSize.h40),
        centerTitle: true,
      ),
      body: RefreshIndicator(
        color: AppColors.primaryColor,
        onRefresh: () async {
          await refreshApiData(context);
        },
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: AppSize.w10),
          child: ListView(
            children: [
              BlocBuilder<HomeCubit, HomeState>(
                builder: (ctx, state) {
                  final homeBloc = ctx.read<HomeCubit>();
                  return Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(vertical: AppSize.h20),
                        child: Stack(
                          children: [
                            Center(
                              child: Text(
                                '${AppLocalizations.of(context)?.summaryWeek} ' +
                                    (appDB.dashBoardMode == 1
                                        ? 'week ${homeBloc.weekNumber}'
                                        : '${homeBloc.monthName}'),
                                textAlign: TextAlign.center,
                                style: context.textTheme.headlineLarge?.copyWith(
                                  color: AppColors.primaryColor,
                                  fontSize: AppSize.sp22,
                                ),
                              ),
                            ),
                            Positioned(
                              right: AppSize.w10,
                              child: GestureDetector(
                                behavior: HitTestBehavior.translucent, // Needed for invisible things to be tapped.

                                onTap: () {
                                  homeBloc.updateSummaryValue(!homeBloc.displayWeek.value);
                                  homeBloc.displayWeek.value
                                      ? homeBloc.plannedShiftForWeekApiData(
                                          context: context,
                                          iosYearWeek:
                                              '${DateTime.now().year}${Week.current().weekNumber.toString().padLeft(2, '0')}')
                                      : homeBloc.plannedShiftFoMonthApiData(
                                          context: context,
                                          ISOYearMonth:
                                              DateFormatFunctions.formatDateTimeToIsoYearMonth(DateTime.now()));

                                  homeBloc.displayWeek.value
                                      ? hourBloc.hoursWeekWorkHourData(
                                          context: context,
                                          isFirstTime: true,
                                          iSOWeek:
                                              '${DateTime.now().year}${Week.current().weekNumber.toString().padLeft(2, '0')}')
                                      : hourBloc.hoursMonthWorkHourData(
                                          context: context,
                                          iSOYearMonth:
                                              DateFormatFunctions.formatDateTimeToIsoYearMonth(DateTime.now()));

                                  appDB.dashBoardMode = homeBloc.displayWeek.value ? 1 : 2;
                                },
                                child: Icon(
                                  Ionicons.swap_horizontal_outline,
                                  size: AppSize.sp24,
                                  color: AppColors.primaryColor,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          persistentTabController.jumpToTab(newIndex = 2);
                        },
                        child: Material(
                          color: context.themeColors.mediumBlackColor,
                          child: Container(
                            width: MediaQuery.of(context).size.width,
                            decoration: BoxDecoration(
                                color: context.themeColors.homeContainerColor,
                                boxShadow: [
                                  BoxShadow(
                                    color: context.themeColors.homeShadowColor,
                                    //spreadRadius: 5,
                                    blurRadius: 2,
                                    offset: Offset(0, 2),
                                  )
                                ],
                                borderRadius: BorderRadius.circular(AppSize.r4)),
                            child: Padding(
                              padding: EdgeInsets.symmetric(vertical: AppSize.h8, horizontal: AppSize.h8),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    AppLocalizations.of(context)!.upComingShift,
                                    style: context.textTheme.titleLarge
                                        ?.copyWith(fontSize: AppSize.sp14, color: context.themeColors.darkGreyColor),
                                  ),
                                  SpaceV(AppSize.h8),
                                  ValueListenableBuilder(
                                    builder: (ctx, isShiftLoading, child) {
                                      if (!homeBloc.isShiftFirstLoading.value) {
                                        if (homeBloc.firstShiftList != null && homeBloc.firstShiftList != '') {
                                          return Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                '${homeBloc.day} ${homeBloc.formattedDate} ${homeBloc.firstShiftList?.TimeFrom} - ${homeBloc.firstShiftList?.TimeUntil}',
                                                style: context.textTheme.titleLarge,
                                              ),
                                              Text(
                                                homeBloc.firstShiftList?.Department ?? '',
                                                style: context.textTheme.titleMedium?.copyWith(
                                                    fontSize: AppSize.sp14, color: context.themeColors.darkGreyColor),
                                              ),
                                            ],
                                          );
                                        } else {
                                          return Column(
                                            children: [
                                              Text(
                                                AppLocalizations.of(context)!.noShiftAvailable,
                                                style: context.textTheme.titleLarge,
                                              ),
                                            ],
                                          );
                                        }
                                      } else {
                                        return HomeShiftContainerShimmerWidget();
                                      }
                                    },
                                    valueListenable: homeBloc.isShiftFirstLoading,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                      SpaceV(AppSize.h40),
                      FittedBox(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            GestureDetector(
                              onTap: () {
                                persistentTabController.jumpToTab(newIndex = 2);
                              },
                              child: Container(
                                width: MediaQuery.of(context).size.width / 2,
                                decoration: BoxDecoration(
                                    color: context.themeColors.homeContainerColor,
                                    boxShadow: [
                                      BoxShadow(
                                        color: context.themeColors.homeShadowColor,
                                        blurRadius: 2,
                                        offset: Offset(0, 2),
                                      )
                                    ],
                                    borderRadius: BorderRadius.circular(AppSize.r4)),
                                child: Padding(
                                  padding: EdgeInsets.symmetric(vertical: AppSize.h8, horizontal: AppSize.h8),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        AppLocalizations.of(context)!.plannedShift,
                                        style: context.textTheme.titleLarge?.copyWith(
                                            fontSize: AppSize.sp14, color: context.themeColors.darkGreyColor),
                                      ),
                                      SpaceV(AppSize.h8),
                                      appDB.dashBoardMode == 1
                                          ? ValueListenableBuilder(
                                              valueListenable: homeBloc.isPlannedWeekLoading,
                                              builder: (context, isPlannedWeekLoading, child) {
                                                if (!isPlannedWeekLoading) {
                                                  return Padding(
                                                    padding: EdgeInsets.symmetric(vertical: AppSize.h10),
                                                    child: Center(
                                                        child: Text(
                                                      homeBloc.planShiftForWeekList.length.toString().isNotEmpty
                                                          ? homeBloc.planShiftForWeekList.length.toString()
                                                          : '0',
                                                      style: context.textTheme.titleMedium?.copyWith(
                                                        fontSize: AppSize.sp40,
                                                        color: AppColors.primaryColor,
                                                      ),
                                                    )),
                                                  );
                                                } else {
                                                  return ShiftShimmerWidget();
                                                }
                                              },
                                            )
                                          : ValueListenableBuilder(
                                              valueListenable: homeBloc.isPlannedMonthLoading,
                                              builder: (context, isPlannedMonthLoading, child) {
                                                if (!isPlannedMonthLoading) {
                                                  return Padding(
                                                    padding: EdgeInsets.symmetric(vertical: AppSize.h10),
                                                    child: Center(
                                                        child: Text(
                                                      homeBloc.planShiftForMonthList.length.toString().isNotEmpty
                                                          ? homeBloc.planShiftForMonthList.length.toString()
                                                          : '0',
                                                      style: context.textTheme.titleMedium?.copyWith(
                                                        fontSize: AppSize.sp40,
                                                        color: AppColors.primaryColor,
                                                      ),
                                                    )),
                                                  );
                                                } else {
                                                  return ShiftShimmerWidget();
                                                }
                                              },
                                            )
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            SpaceH(AppSize.w10),
                            GestureDetector(
                              onTap: () {
                                persistentTabController.jumpToTab(newIndex = 3);
                              },
                              child: Container(
                                width: MediaQuery.of(context).size.width / 2,
                                decoration: BoxDecoration(
                                    color: context.themeColors.homeContainerColor,
                                    boxShadow: [
                                      BoxShadow(
                                        color: context.themeColors.homeShadowColor,
                                        //spreadRadius: 5,
                                        blurRadius: 2,
                                        offset: Offset(0, 2),
                                      )
                                    ],
                                    borderRadius: BorderRadius.circular(AppSize.r4)),
                                child: Padding(
                                  padding: EdgeInsets.symmetric(vertical: AppSize.h8, horizontal: AppSize.h8),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        AppLocalizations.of(context)!.workHour,
                                        style: context.textTheme.titleLarge?.copyWith(
                                            fontSize: AppSize.sp14, color: context.themeColors.darkGreyColor),
                                      ),
                                      SpaceV(AppSize.h8),
                                      appDB.dashBoardMode == 1
                                          ? ValueListenableBuilder(
                                              valueListenable: hourBloc.isWeekLoading,
                                              builder: (context, isWeekLoading, child) {
                                                if (!isWeekLoading) {
                                                  return Padding(
                                                    padding: EdgeInsets.symmetric(vertical: AppSize.h10),
                                                    child: Center(
                                                        child: Text(
                                                      hourBloc.workWeekHour.isNotEmpty
                                                          ? hourBloc.workWeekHour
                                                          : "00:00",
                                                      style: context.textTheme.titleMedium?.copyWith(
                                                        fontSize: AppSize.sp40,
                                                        color: AppColors.primaryColor,
                                                      ),
                                                    )),
                                                  );
                                                } else {
                                                  return WorkHourShimmerWidget();
                                                }
                                              },
                                            )
                                          : ValueListenableBuilder(
                                              valueListenable: hourBloc.isHourLoading,
                                              builder: (context, isHourLoading, child) {
                                                if (!isHourLoading) {
                                                  return Padding(
                                                    padding: EdgeInsets.symmetric(vertical: AppSize.h10),
                                                    child: Center(
                                                        child: Text(
                                                      hourBloc.workMonthHour.isNotEmpty
                                                          ? hourBloc.workMonthHour
                                                          : "00:00",
                                                      style: context.textTheme.titleMedium?.copyWith(
                                                        fontSize: AppSize.sp40,
                                                        color: AppColors.primaryColor,
                                                      ),
                                                    )),
                                                  );
                                                } else {
                                                  return WorkHourShimmerWidget();
                                                }
                                              },
                                            )
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                },
              ),
              SpaceV(AppSize.h10),
              ValueListenableBuilder(
                valueListenable: newsBloc.isLoading,
                builder: (BuildContext context, isLoading, Widget? child) {
                  if (!isLoading && newsBloc.newsList.isNotEmpty) {
                    return ListView.builder(
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      itemCount: 5,
                      itemBuilder: (context, index) {
                        return GestureDetector(
                          onTap: () {
                            persistentTabController.jumpToTab(newIndex = 4);

                            if (firstTimeNewScreenNavigate) {
                              Future.delayed(
                                Duration(seconds: 1),
                                () {
                                  HomeScreen.itemScrollController
                                      .scrollTo(index: index, duration: Duration(milliseconds: 500));
                                },
                              );
                              firstTimeNewScreenNavigate = false;
                            } else {
                              HomeScreen.itemScrollController
                                  .scrollTo(index: index, duration: Duration(milliseconds: 500));
                            }
                          },
                          child: Column(
                            children: [
                              HomeNewContainerWidget(
                                isNews: index == 0 ? true : false,
                                newsDate: NewsScreen.formatDate(newsBloc.newsList[index].startDate ?? DateTime.now()),
                                newsText: newsBloc.newsList[index].title,
                                index: index,
                              ),
                            ],
                          ),
                        );
                      },
                    );
                  } else if (!isLoading && newsBloc.newsList.isEmpty) {
                    return Container(
                        height: MediaQuery.of(context).size.height * 0.3,
                        width: MediaQuery.of(context).size.width,
                        child: Center(child: Text(AppLocalizations.of(context)!.noNewFound)));
                  } else {
                    return ListView.builder(
                      physics: NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                      itemCount: appDB.newsItem,
                      itemBuilder: (context, index) {
                        return HomeNewContainerShimmerWidget();
                      },
                    );
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

Future<void> refreshApiData(BuildContext context) async {
  final homeBloc = BlocProvider.of<HomeCubit>(
    context,
  );
  final newsBloc = BlocProvider.of<NewsCubit>(
    context,
  );

  final hourBloc = BlocProvider.of<HoursCubit>(
    context,
  );
  newsBloc.newsAndProfileApiCall(context: context);
  homeBloc.plannedShiftForWeekApiData(
      context: context, iosYearWeek: '${DateTime.now().year}${Week.current().weekNumber.toString().padLeft(2, '0')}');
  homeBloc.plannedShiftFoMonthApiData(
      context: context, ISOYearMonth: DateFormatFunctions.formatDateTimeToIsoYearMonth(DateTime.now()));

  hourBloc.hoursMonthWorkHourData(
      context: context,
      isFirstTime: true,
      iSOYearMonth: DateFormatFunctions.formatDateTimeToIsoYearMonth(DateTime.now()));

  hourBloc.hoursWeekWorkHourData(
      context: context,
      isFirstTime: true,
      iSOWeek: '${DateTime.now().year}${Week.current().weekNumber.toString().padLeft(2, '0')}');
  homeBloc.firstUpcomingShift(context: context).then(
    (value) {
      print("homeBloc.firstShiftList?.Date.toString() ${homeBloc.firstShiftList?.Date.toString()}");
      DateTime date = DateTime.parse(homeBloc.firstShiftList?.Date.toString() ?? '');
      DateFormat outputFormat = DateFormat("d MMMM", appDB.language);
      homeBloc.formattedDate = outputFormat.format(date);

      DateTime shiftDay =
          DateFormat("yyyy-MM-dd'T'HH:mm:ss", appDB.language).parse(homeBloc.firstShiftList?.Date.toString() ?? '');
      homeBloc.day = DateFormat('EEEE', appDB.language).format(shiftDay);
      log("date =======>${homeBloc.day}");

      DateTime today = DateTime.now();
      homeBloc.monthName = DateFormat.MMMM().format(today);
    },
  );
}

import 'package:bloc/bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'package:staff_medewerker/common/custom_widgets/common_snackbar.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/open_service_module/model/my_service_per_month.dart';
import 'package:staff_medewerker/screens/open_service_module/repository/open_service_repository.dart';

part 'open_service_state.dart';

class OpenServiceCubit extends Cubit<OpenServiceState> {
  OpenServiceCubit() : super(OpenServiceInitial());

  final OpenServiceApiRepository openServiceApi = OpenServiceApiRepository();

  ValueNotifier<bool> isOpenServiceMonthLoading = ValueNotifier(false);
  ValueNotifier<bool> isSetSwapShiftLoading = ValueNotifier(false);
  ValueNotifier<bool> toggleMyOpenServiceSubscriptionLoading = ValueNotifier(false);
  ValueNotifier<bool> isCancelSwapShiftLoading = ValueNotifier(false);
  TextEditingController swapNoteController = TextEditingController();
  DateTime selectDate = DateTime.now();

  List<MyServicesPerMonthResponseModel> myServicePerMonthList = [];

  Future<void> myServicesPerMonthData({
    required BuildContext context,
    required String iosYearMonth,
  }) async {
    isOpenServiceMonthLoading.value = true;

    final response = await openServiceApi.myServicesPerMonthApi(context: context, iosYearMonth: iosYearMonth);
    myServicePerMonthList.clear();
    myServicePerMonthList = response!;

    print("========================>${myServicePerMonthList}");

    isOpenServiceMonthLoading.value = false;

    emit(OpenServiceInitial());
  }

  Future<void> fetchMonthData(BuildContext context, {bool isApiCall = true}) async {
    String formattedDate = DateFormat('yyyyMM').format(selectDate);
    print("formatted date ======>${formattedDate}");
    await scheduleMonthData(
      context: context,
      iosYearMonth: formattedDate,
    );
    toggleMyOpenServiceSubscriptionLoading.value = false;
    emit(OpenServiceInitial());
  }

  Future<void> scheduleMonthData({
    required BuildContext context,
    required String iosYearMonth,
  }) async {
    isOpenServiceMonthLoading.value = true;

    final response = await openServiceApi.myServicesPerMonthApi(context: context, iosYearMonth: iosYearMonth);
    myServicePerMonthList.clear();
    myServicePerMonthList = response!;

    isOpenServiceMonthLoading.value = false;

    emit(OpenServiceInitial());
  }

  Future<void> setSwapAndServiceApiData(
      {required BuildContext context,
      required String personId,
      required String dateEntryId,
      required String stateId,
      required String nextStateId,
      required String remark}) async {
    isSetSwapShiftLoading.value = true;
    final response = await openServiceApi.setSwapAndServiceApi(
      context: context,
      personId: personId,
      dateEntryId: dateEntryId,
      nextStateId: nextStateId,
      stateId: stateId,
      remark: remark,
    );
    isSetSwapShiftLoading.value = false;

    if (response?.statusCode == 200 && response?.data['Done'] == true) {
      customSnackBar(
        context: navigatorKey.currentContext!,
        message: AppLocalizations.of(navigatorKey.currentContext!)!.requestToSwap,
        actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!.closeText.toUpperCase(),
      );
    }
  }

  Future<void> toggleMyOpenServiceSubscriptionApiData({
    required BuildContext context,
    required String personId,
    required String baseServiceId,
    required String departmentId,
    required String date,
    required bool isSubscribe,
  }) async {
    toggleMyOpenServiceSubscriptionLoading.value = true;
    final response = await openServiceApi.toggleMyOpenServiceSubscriptionApi(
      context: context,
      personId: personId,
      baseServiceId: baseServiceId,
      departmentId: departmentId,
      date: date,
    );

    if (response?.done == "" && response?.statusCode == 200) {
      isSubscribe
          ? customSnackBar(
              context: navigatorKey.currentContext!,
              message: AppLocalizations.of(navigatorKey.currentContext!)!.subscribeSuccess,
              actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!.closeText.toUpperCase(),
            )
          : customSnackBar(
              context: navigatorKey.currentContext!,
              message: AppLocalizations.of(navigatorKey.currentContext!)!.unsubscribeSuccess,
              actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!.closeText.toUpperCase(),
            );
    }
  }

  Future<void> cancelSwapAndServiceApiData({
    required BuildContext context,
    required String personId,
    required String dateEntryId,
    required String stateId,
    required String nextStateId,
  }) async {
    isCancelSwapShiftLoading.value = true;
    final response = await openServiceApi.cancelSwapAndServiceApi(
      context: context,
      personId: personId,
      dateEntryId: dateEntryId,
      nextStateId: nextStateId,
      stateId: stateId,
    );
    isCancelSwapShiftLoading.value = false;

    if (response?.statusCode == 200 && response?.data['Done'] == true) {
      customSnackBar(
        context: navigatorKey.currentContext!,
        message: AppLocalizations.of(navigatorKey.currentContext!)!.requestToSwap,
        actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!.closeText.toUpperCase(),
      );
    }
  }
}

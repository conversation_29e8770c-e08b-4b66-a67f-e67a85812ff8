import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/authentication_module/widget/common_button.dart';
import 'package:staff_medewerker/screens/open_service_module/bloc/open_service_cubit.dart';
import 'package:staff_medewerker/screens/open_service_module/model/my_service_per_month.dart';
import 'package:staff_medewerker/screens/profile_module/widget/common/common_info_row.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';
import 'package:staff_medewerker/utils/constant/constant.dart';

class OpenServiceDetailScreen extends StatelessWidget {
  final MyServicesPerMonthResponseModel? myServiceMonthData;

  const OpenServiceDetailScreen({Key? key, this.myServiceMonthData}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    log("=====>${myServiceMonthData?.department}");
    log("scheduleWeekData?.swap?.state=====>${myServiceMonthData?.swap?.state}");

    return Scaffold(
      appBar: CustomAppBar(
          title: DateFormat('EEEE d MMMM', appDB.language)
              .format(DateTime.parse(myServiceMonthData?.iSODate.toString() ?? ''))),
      backgroundColor: context.themeColors.homeContainerColor,
      resizeToAvoidBottomInset: true,
      body: Padding(
        padding: const EdgeInsets.only(top: 20, left: 20, right: 20),
        child: SingleChildScrollView(
          child: BlocBuilder<OpenServiceCubit, OpenServiceState>(
            builder: (ctx, state) {
              final openServiceBloc = ctx.read<OpenServiceCubit>();

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SpaceV(AppSize.h10),
                  CommonInfoRow(
                    title: AppLocalizations.of(context)!.startScheduleTime,
                    value: myServiceMonthData?.timeFrom ?? '-',
                    titleValue: context.textTheme.titleMedium
                        ?.copyWith(fontSize: AppSize.sp13, color: context.themeColors.darkGreyColor),
                    titlePadding: 0,
                  ),
                  CommonInfoRow(
                    title: AppLocalizations.of(context)!.endTime,
                    value: myServiceMonthData?.timeUntil ?? '-',
                    titleValue: context.textTheme.titleMedium
                        ?.copyWith(fontSize: AppSize.sp13, color: context.themeColors.darkGreyColor),
                    titlePadding: 0,
                  ),
                  CommonInfoRow(
                    title: AppLocalizations.of(context)!.department,
                    value: myServiceMonthData?.department ?? '-',
                    titleValue: context.textTheme.titleMedium
                        ?.copyWith(fontSize: AppSize.sp13, color: context.themeColors.darkGreyColor),
                    titlePadding: 0,
                  ),
                  CommonInfoRow(
                    title: AppLocalizations.of(context)!.breakScheduleText,
                    value: myServiceMonthData?.breakTime ?? '-',
                    titleValue: context.textTheme.titleMedium
                        ?.copyWith(fontSize: AppSize.sp13, color: context.themeColors.darkGreyColor),
                    titlePadding: 0,
                  ),
                  CommonInfoRow(
                    title: AppLocalizations.of(context)!.costCenter,
                    value: myServiceMonthData?.costCenters ?? '-',
                    titleValue: context.textTheme.titleMedium
                        ?.copyWith(fontSize: AppSize.sp13, color: context.themeColors.darkGreyColor),
                    titlePadding: 0,
                  ),
                  CommonInfoRow(
                    title: AppLocalizations.of(context)!.service,
                    value: myServiceMonthData?.service ?? '-',
                    titleValue: context.textTheme.titleMedium
                        ?.copyWith(fontSize: AppSize.sp13, color: context.themeColors.darkGreyColor),
                    titlePadding: 0,
                  ),
                  CommonInfoRow(
                    title: AppLocalizations.of(context)!.remark,
                    value: myServiceMonthData?.remark ?? '-',
                    titleValue: context.textTheme.titleMedium
                        ?.copyWith(fontSize: AppSize.sp13, color: context.themeColors.darkGreyColor),
                    titlePadding: 0,
                  ),
                  CommonInfoRow(
                    title: AppLocalizations.of(context)!.dayRemark,
                    value: myServiceMonthData?.dayRemark ?? '-',
                    titleValue: context.textTheme.titleMedium
                        ?.copyWith(fontSize: AppSize.sp13, color: context.themeColors.darkGreyColor),
                    titlePadding: 0,
                  ),
                  SpaceV(AppSize.h20),
                  (myServiceMonthData?.openService != null && myServiceMonthData?.openService?.subscribed == false)
                      ? Center(
                          child: Padding(
                            padding: EdgeInsets.only(
                              left: AppSize.w90,
                              right: AppSize.w90,
                            ),
                            child: ValueListenableBuilder(
                              valueListenable: openServiceBloc.toggleMyOpenServiceSubscriptionLoading,
                              builder: (context, toggleMyOpenServiceSubscriptionLoading, child) {
                                return CommonButton(
                                  isLoading: toggleMyOpenServiceSubscriptionLoading,
                                  height: AppSize.h36,
                                  title: AppLocalizations.of(context)!.subscribe,
                                  buttonColor: AppColors.primaryColor,
                                  titleStyle: context.textTheme.bodyMedium?.copyWith(
                                      fontSize: AppSize.sp14,
                                      color: AppColors.white,
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: 1),
                                  onPressed: () {
                                    openServiceBloc.toggleMyOpenServiceSubscriptionApiData(
                                      context: context,
                                      baseServiceId: myServiceMonthData?.openService?.baseServiceId ?? '',
                                      departmentId: myServiceMonthData?.openService?.departmentId ?? '',
                                      date: myServiceMonthData?.date ?? '',
                                      personId: myServiceMonthData?.personId ?? '',
                                      isSubscribe: true,
                                    )
                                      ..then((value) => Navigator.pop(context))
                                      ..then((value) => openServiceBloc.fetchMonthData(context, isApiCall: true));
                                  },
                                );
                              },
                            ),
                          ),
                        )
                      : (myServiceMonthData?.openService != null && myServiceMonthData?.openService?.subscribed == true)
                          ? Center(
                              child: Padding(
                                padding: EdgeInsets.only(
                                  left: AppSize.w90,
                                  right: AppSize.w90,
                                ),
                                child: ValueListenableBuilder(
                                  valueListenable: openServiceBloc.toggleMyOpenServiceSubscriptionLoading,
                                  builder: (BuildContext context, bool toggleMyOpenServiceSubscriptionLoading,
                                      Widget? child) {
                                    return CommonButton(
                                      isLoading: toggleMyOpenServiceSubscriptionLoading,
                                      height: AppSize.h36,
                                      title: AppLocalizations.of(context)!.unsubscribe,
                                      buttonColor: AppColors.lightModeRedColor,
                                      titleStyle: context.textTheme.bodyMedium?.copyWith(
                                          fontSize: AppSize.sp14,
                                          color: AppColors.white,
                                          fontWeight: FontWeight.w500,
                                          letterSpacing: 1),
                                      onPressed: () {
                                        openServiceBloc.toggleMyOpenServiceSubscriptionApiData(
                                          context: context,
                                          baseServiceId: myServiceMonthData?.openService?.baseServiceId ?? '',
                                          departmentId: myServiceMonthData?.openService?.departmentId ?? '',
                                          date: myServiceMonthData?.date ?? '',
                                          personId: myServiceMonthData?.personId ?? '',
                                          isSubscribe: false,
                                        )
                                          ..then((value) => Navigator.pop(context))
                                          ..then((value) => openServiceBloc.fetchMonthData(context, isApiCall: true));
                                      },
                                    );
                                  },
                                ),
                              ),
                            )
                          : myServiceMonthData?.swap != null && myServiceMonthData?.swap?.stateId == null
                              ? Padding(
                                  padding: EdgeInsets.only(
                                    left: AppSize.w110,
                                    right: AppSize.w110,
                                  ),
                                  child: ValueListenableBuilder<bool>(
                                    valueListenable: openServiceBloc.isSetSwapShiftLoading,
                                    builder: (BuildContext context, isSetSwapShiftLoading, Widget? child) {
                                      return CommonButton(
                                        height: AppSize.h36,
                                        isLoading: isSetSwapShiftLoading,
                                        title: AppLocalizations.of(context)!.swapShift,
                                        titleStyle: context.textTheme.bodyMedium?.copyWith(
                                            fontSize: AppSize.sp14,
                                            color: AppColors.white,
                                            fontWeight: FontWeight.w500,
                                            letterSpacing: 1),
                                        onPressed: () {
                                          openServiceBloc.swapNoteController.clear();
                                          showDialog(
                                            context: context,
                                            builder: (dialogContext) {
                                              return AlertDialog(
                                                backgroundColor: context.themeColors.cardColor,
                                                title: Text(AppLocalizations.of(context)!.youAreAboutSwapShift,
                                                    style: context.textTheme.headlineLarge?.copyWith(
                                                      fontSize: AppSize.sp18,
                                                      fontWeight: FontWeight.w500,
                                                      color: context.themeColors.textColor,
                                                    )),
                                                content: TextField(
                                                  controller: openServiceBloc.swapNoteController,
                                                  decoration: InputDecoration(
                                                    hintText: AppLocalizations.of(context)!.noteForColleagues,
                                                    hintStyle: context.textTheme.bodyMedium?.copyWith(
                                                      color: context.themeColors.textColor.withOpacity(0.5),
                                                    ),
                                                  ),
                                                ),
                                                actions: <Widget>[
                                                  Row(
                                                    mainAxisAlignment: MainAxisAlignment.center,
                                                    children: [
                                                      TextButton(
                                                        onPressed: () {
                                                          Navigator.of(context).pop(); // Close the dialog
                                                        },
                                                        child: Text(
                                                          AppLocalizations.of(context)!.noCancel.toUpperCase(),
                                                          style: context.textTheme.headlineLarge?.copyWith(
                                                            fontSize: AppSize.sp13,
                                                            fontWeight: FontWeight.normal,
                                                            color: context.themeColors.primaryColor,
                                                          ),
                                                        ),
                                                      ),
                                                      SpaceH(AppSize.w14),
                                                      TextButton(
                                                        onPressed: () async {
                                                          Navigator.of(dialogContext).pop();
                                                          openServiceBloc.setSwapAndServiceApiData(
                                                              context: context,
                                                              personId: myServiceMonthData?.swap?.personId ?? '',
                                                              dateEntryId: myServiceMonthData?.swap?.dateEntryId ?? '',
                                                              stateId: myServiceMonthData?.swap?.stateId ?? '',
                                                              nextStateId: '77151add-11f2-432a-a7f6-2b65bedcc6db',
                                                              remark: openServiceBloc.swapNoteController.text)
                                                            ..then((value) => Navigator.pop(context))
                                                            ..then((value) => openServiceBloc.fetchMonthData(context,
                                                                isApiCall: true));
                                                        },
                                                        child: Text(
                                                          AppLocalizations.of(context)!.yesSwapShift.toUpperCase(),
                                                          style: context.textTheme.headlineLarge?.copyWith(
                                                            fontSize: AppSize.sp13,
                                                            fontWeight: FontWeight.normal,
                                                            color: context.themeColors.primaryColor,
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  )
                                                ],
                                              );
                                            },
                                          );
                                        },
                                      );
                                    },
                                  ),
                                )
                              : (myServiceMonthData?.swap?.state == 'Aangevraagd')
                                  ? Center(
                                      child: Padding(
                                        padding: EdgeInsets.only(
                                          left: AppSize.w90,
                                          right: AppSize.w90,
                                        ),
                                        child: ValueListenableBuilder(
                                          valueListenable: openServiceBloc.isCancelSwapShiftLoading,
                                          builder: (BuildContext context, isCancelSwapShiftLoading, Widget? child) {
                                            return CommonButton(
                                              isLoading: isCancelSwapShiftLoading,
                                              height: AppSize.h36,
                                              title: AppLocalizations.of(context)!.cancelSwap.toUpperCase(),
                                              buttonColor: AppColors.lightModeRedColor,
                                              titleStyle: context.textTheme.bodyMedium?.copyWith(
                                                  fontSize: AppSize.sp14,
                                                  color: AppColors.white,
                                                  fontWeight: FontWeight.w500,
                                                  letterSpacing: 1),
                                              onPressed: () {
                                                openServiceBloc.cancelSwapAndServiceApiData(
                                                  context: context,
                                                  personId: myServiceMonthData?.swap?.personId ?? '',
                                                  dateEntryId: myServiceMonthData?.swap?.dateEntryId ?? '',
                                                  stateId: myServiceMonthData?.swap?.stateId ?? '',
                                                  nextStateId: '',
                                                )
                                                  ..then((value) => Navigator.pop(context))
                                                  ..then((value) =>
                                                      openServiceBloc.fetchMonthData(context, isApiCall: true));
                                              },
                                            );
                                          },
                                        ),
                                      ),
                                    )
                                  : (myServiceMonthData?.swap?.state == 'Geaccepteerd')
                                      ? Text(
                                          AppLocalizations.of(context)!.youHaveRegistered,
                                          overflow: TextOverflow.ellipsis,
                                          style: context.textTheme.titleMedium
                                              ?.copyWith(fontSize: AppSize.sp14, color: AppColors.primaryColor),
                                        )
                                      : (myServiceMonthData?.swap?.state == 'Uitgezet')
                                          ? Column(
                                              children: [
                                                Row(
                                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                  children: [
                                                    ValueListenableBuilder(
                                                      valueListenable: openServiceBloc.isCancelSwapShiftLoading,
                                                      builder: (BuildContext context, bool isCancelSwapShiftLoading,
                                                          Widget? child) {
                                                        return CommonButton(
                                                          isLoading: isCancelSwapShiftLoading,
                                                          height: AppSize.h36,
                                                          title: AppLocalizations.of(context)!.refuse.toUpperCase(),
                                                          buttonColor: AppColors.lightModeRedColor,
                                                          width: AppSize.w80,
                                                          titleStyle: context.textTheme.bodyMedium?.copyWith(
                                                              fontSize: AppSize.sp14,
                                                              color: AppColors.white,
                                                              fontWeight: FontWeight.w500,
                                                              letterSpacing: 1),
                                                          onPressed: () {
                                                            openServiceBloc.cancelSwapAndServiceApiData(
                                                              context: context,
                                                              personId: myServiceMonthData?.swap?.personId ?? '',
                                                              dateEntryId: myServiceMonthData?.swap?.dateEntryId ?? '',
                                                              stateId: myServiceMonthData?.swap?.stateId ?? '',
                                                              nextStateId: '40a784ce-78eb-44be-8a98-76806c2d24c9',
                                                            )
                                                              ..then((value) => Navigator.pop(context))
                                                              ..then(
                                                                  (value) => openServiceBloc.fetchMonthData(context));
                                                          },
                                                        );
                                                      },
                                                    ),
                                                    ValueListenableBuilder(
                                                      valueListenable: openServiceBloc.isCancelSwapShiftLoading,
                                                      builder: (BuildContext context, bool isCancelSwapShiftLoading,
                                                          Widget? child) {
                                                        return CommonButton(
                                                          isLoading: isCancelSwapShiftLoading,
                                                          height: AppSize.h36,
                                                          title:
                                                              AppLocalizations.of(context)!.registerShift.toUpperCase(),
                                                          buttonColor: AppColors.primaryColor,
                                                          width: AppSize.w200,
                                                          titleStyle: context.textTheme.bodyMedium?.copyWith(
                                                              fontSize: AppSize.sp14,
                                                              color: AppColors.white,
                                                              fontWeight: FontWeight.w500,
                                                              letterSpacing: 1),
                                                          onPressed: () {
                                                            openServiceBloc.cancelSwapAndServiceApiData(
                                                              context: context,
                                                              personId: '',
                                                              dateEntryId: myServiceMonthData?.swap?.dateEntryId ?? '',
                                                              stateId: myServiceMonthData?.swap?.stateId ?? '',
                                                              nextStateId: '1ace5656-d483-4c5a-b561-0ebd583f6cf3',
                                                            )
                                                              ..then((value) => Navigator.pop(context))
                                                              ..then(
                                                                  (value) => openServiceBloc.fetchMonthData(context));
                                                          },
                                                        );
                                                      },
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            )
                                          : Container()
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/balance_screen/repository/my_balances_overview/balance_overview_model.dart';
import 'package:staff_medewerker/screens/balance_screen/repository/my_balances_overview/my_balances_overview_api_repository.dart';

class BalanceCubit extends Cubit<bool> {
  BalanceCubit() : super(false);

  ValueNotifier<bool> isLoading = ValueNotifier(false);
  String selectedDate = "";
  ValueNotifier<String> selectedDateString =
      ValueNotifier('${_formatDate(DateTime.now())}');

  static String _formatDate(DateTime date) {
    final formatter = DateFormat('E dd MMM', appDB.language);
    return formatter.format(date);
  }

  ValueNotifier<MyBalancesOverviewModel> myBalancesOverview = ValueNotifier(
      MyBalancesOverviewModel(
          sequence: 0,
          categorie: "",
          total: false,
          vak: "",
          atv: "",
          totaal: ""));

  Future<void> myBalancesOverviewApiCall({
    required BuildContext context,
    required String selectedDate,
    bool isFirstTime = false,
  }) async {
    isLoading.value = true;
    print("myBalancesOverviewApiCall started =====> $selectedDate");

    final MyBalancesOverviewRepository myBalancesOverviewRepository =
        MyBalancesOverviewRepository();
    final response = await myBalancesOverviewRepository.myBalancesOverviewApi(
      context: context,
      selectedDate: selectedDate,
    );

    if (response != null && response.isNotEmpty) {
      // Find the total row (where total = true)
      MyBalancesOverviewModel? totalModel = response.firstWhere(
        (element) => element.total == true,
        orElse: () => MyBalancesOverviewModel(
          sequence: 0,
          categorie: "",
          total: false,
          vak: "",
          atv: "",
          totaal: "",
        ),
      );

      myBalancesOverview.value = totalModel;
      print("myBalancesOverview: ${myBalancesOverview.value.totaal}");
    } else {
      // Set empty values if no data
      myBalancesOverview.value = MyBalancesOverviewModel(
        sequence: 0,
        categorie: "",
        total: false,
        vak: "-",
        atv: "-",
        tvt: "-",
        sparen: "-",
        totaal: "-",
      );
    }

    isLoading.value = false;
    emit(false);
  }

  void setDate({required DateTime selectedValue}) {
    selectedDateString.value = dateToString(selectedValue);
  }

  String dateToString(DateTime date) {
    return _formatDate(date);
  }

  String formatDate(DateTime date) {
    return _formatDate(date);
  }

  DateTime stringToDate(String dateString) {
    try {
      // Parse the date string back to DateTime
      // Assuming the format is similar to "Mon 25 Dec"
      List<String> parts = dateString.split(' ');
      if (parts.length >= 3) {
        int day = int.parse(parts[1]);
        String monthStr = parts[2];
        int year = DateTime.now().year; // Use current year as default

        Map<String, int> months = {
          'Jan': 1,
          'Feb': 2,
          'Mar': 3,
          'Apr': 4,
          'May': 5,
          'Jun': 6,
          'Jul': 7,
          'Aug': 8,
          'Sep': 9,
          'Oct': 10,
          'Nov': 11,
          'Dec': 12
        };

        int month = months[monthStr] ?? DateTime.now().month;
        return DateTime(year, month, day);
      }
    } catch (e) {
      print("Error parsing date string: $e");
    }
    return DateTime.now();
  }

  @override
  Future<void> close() {
    isLoading.dispose();
    selectedDateString.dispose();
    myBalancesOverview.dispose();
    return super.close();
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:ionicons/ionicons.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:staff_medewerker/common/common_functions/common_dateformat_function.dart';
import 'package:staff_medewerker/common/custom_date_picker/date_picker.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/shimmer_effect.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/screens/balance_screen/repository/my_balances_overview/balance_overview_model.dart';
import 'package:staff_medewerker/screens/balance_screen/bloc/balance_screen_cubit.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

class BalanceScreen extends StatefulWidget {
  const BalanceScreen({Key? key}) : super(key: key);

  @override
  State<BalanceScreen> createState() => _BalanceScreenState();
}

class _BalanceScreenState extends State<BalanceScreen> {
  late BalanceCubit balanceCubit;

  @override
  void initState() {
    super.initState();
    balanceCubit = BlocProvider.of<BalanceCubit>(context);
    balanceCubit.selectedDate = DateFormatFunctions.formatDate(DateTime.now());
    balanceCubit.selectedDateString.value =
        balanceCubit.dateToString(DateTime.now());

    print("balanceCubit.selectedDate: ${balanceCubit.selectedDate}");
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      await balanceCubit.myBalancesOverviewApiCall(
          context: context,
          selectedDate: balanceCubit.selectedDate,
          isFirstTime: true);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.themeColors.listGridColor1,
      appBar: CustomAppBar(title: AppLocalizations.of(context)!.saldoText),
      body: Column(
        children: [
          // Date picker section
          InkWell(
            onTap: () async {
              DateTime initialDate2 = balanceCubit
                  .stringToDate(balanceCubit.selectedDateString.value);
              print("initialDate: $initialDate2");

              DateTime? selectedDate2 = await CustomDatePicker(
                context: context,
                initialDate: initialDate2,
                firstDate: DateTime(1950),
                lastDate: DateTime(2100),
                initialEntryMode: DatePickerEntryMode.calendarOnly,
                confirmText: AppLocalizations.of(context)!.oK.toUpperCase(),
                cancelText:
                    AppLocalizations.of(context)!.closeText.toUpperCase(),
                builder: (context, child) {
                  return Theme(
                    data: ThemeData.light().copyWith(
                        colorScheme: ColorScheme.dark(
                            onPrimary: AppColors.white, // selected text color
                            onSurface: context.themeColors
                                .textColor, // default date text color
                            primary: AppColors.primaryColor, // circle color
                            surface: context
                                .themeColors.drawerColor // background color,
                            ),
                        dialogBackgroundColor:
                            context.themeColors.listGridColor1,
                        textButtonTheme: TextButtonThemeData(
                            style: TextButton.styleFrom(
                                foregroundColor: AppColors.primaryColor))),
                    child: child!,
                  );
                },
              );

              if (selectedDate2 != null) {
                // A date was selected by the user
                if (selectedDate2 != initialDate2) {
                  balanceCubit.setDate(selectedValue: selectedDate2);

                  String date = DateFormatFunctions.formatDate(selectedDate2);

                  balanceCubit.myBalancesOverviewApiCall(
                      context: context, selectedDate: date);
                  print("Selected date is: $selectedDate2");
                  print("date: $selectedDate2");
                } else {
                  // The user selected the same date as the initial date
                  print("User selected the same date as the initial date.");
                }
              } else {
                // No date was selected by the user
                print("No date was selected by the user.");
              }
            },
            child: Container(
              height: AppSize.h40,
              color: context.themeColors.listGridColor1,
              padding: EdgeInsets.symmetric(horizontal: AppSize.w12),
              alignment: Alignment.centerLeft,
              child: Row(
                children: [
                  Text(
                    AppLocalizations.of(context)!.balanceText,
                    style: context.textTheme.bodyMedium?.copyWith(
                      color: context.themeColors.textColor,
                      fontSize: AppSize.sp15,
                    ),
                  ),
                  Spacer(),
                  ValueListenableBuilder(
                    valueListenable: balanceCubit.selectedDateString,
                    builder: (BuildContext context, value, Widget? child) {
                      return Text(
                        "${value}",
                        style: context.textTheme.bodyMedium?.copyWith(
                            color: context.themeColors.textColor,
                            fontSize: AppSize.sp15),
                      );
                    },
                  ),
                  SpaceH(AppSize.w6),
                  Icon(Ionicons.calendar,
                      color: context.themeColors.textColor, size: AppSize.sp16),
                ],
              ),
            ),
          ),
          Divider(
            color: context.themeColors.greyColor,
          ),

          // Balance data section
          ValueListenableBuilder(
            valueListenable: balanceCubit.isLoading,
            builder: (BuildContext context, bool isLoading, Widget? child) {
              // if (isLoading) {
              //   return Column(
              //     children: [
              //       ShimmerWidget(
              //         margin: EdgeInsets.symmetric(
              //             horizontal: AppSize.h10, vertical: AppSize.w14),
              //         height: AppSize.h10,
              //       ),
              //       Divider(
              //         color: context.themeColors.greyColor,
              //       ),
              //       ShimmerWidget(
              //         margin: EdgeInsets.symmetric(
              //             horizontal: AppSize.h10, vertical: AppSize.w14),
              //         height: AppSize.h10,
              //       ),
              //       Divider(
              //         color: context.themeColors.greyColor,
              //       ),
              //       ShimmerWidget(
              //         margin: EdgeInsets.symmetric(
              //             horizontal: AppSize.h10, vertical: AppSize.w14),
              //         height: AppSize.h10,
              //       ),
              //       Divider(
              //         color: context.themeColors.greyColor,
              //       ),
              //       ShimmerWidget(
              //         margin: EdgeInsets.symmetric(
              //             horizontal: AppSize.h10, vertical: AppSize.w14),
              //         height: AppSize.h10,
              //       ),
              //       Divider(
              //         color: context.themeColors.greyColor,
              //       ),
              //       ShimmerWidget(
              //         margin: EdgeInsets.symmetric(
              //             horizontal: AppSize.h10, vertical: AppSize.w14),
              //         height: AppSize.h10,
              //       ),
              //     ],
              //   );
              // } else {
              return Skeletonizer(
                enabled: isLoading,
                child: ValueListenableBuilder(
                  valueListenable: balanceCubit.myBalancesOverview,
                  builder: (BuildContext context, MyBalancesOverviewModel value,
                      Widget? child) {
                    return Flexible(
                      child: SingleChildScrollView(
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: AppSize.w10, vertical: AppSize.h10),
                          // padding: const EdgeInsets.all(8.0),
                          child: Column(
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  color: context.themeColors.homeContainerColor,
                                  border: Border.all(
                                      color: context.themeColors.textColor),
                                ),
                                padding: EdgeInsets.symmetric(
                                    horizontal: AppSize.w16,
                                    vertical: AppSize.h10),
                                margin: EdgeInsets.symmetric(
                                    vertical: AppSize.h10,
                                    horizontal: AppSize.w20),
                                child: Column(
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Icon(Icons.chalet_outlined,
                                            size: AppSize.sp34),
                                        SpaceH(AppSize.w10),
                                        Text(
                                          AppLocalizations.of(context)!.absence,
                                          style: context.textTheme.bodyMedium
                                              ?.copyWith(
                                            color:
                                                context.themeColors.textColor,
                                            fontSize: AppSize.sp20,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        )
                                      ],
                                    ),
                                    SpaceV(AppSize.h10),
                                    BalanceDataItem(
                                        title: AppLocalizations.of(context)!
                                            .vacationText,
                                        data: value.vak ?? '-'),
                                    BalanceDataItem(
                                        title: "ATV", data: value.atv ?? '-'),
                                  ],
                                ),
                              ),
                              Container(
                                decoration: BoxDecoration(
                                  color: context.themeColors.homeContainerColor,
                                  border: Border.all(
                                      color: context.themeColors.textColor),
                                ),
                                padding: EdgeInsets.symmetric(
                                    horizontal: AppSize.w16,
                                    vertical: AppSize.h10),
                                margin: EdgeInsets.symmetric(
                                    vertical: AppSize.h10,
                                    horizontal: AppSize.w20),
                                child: Column(
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Icon(Icons.access_time,
                                            size: AppSize.sp26),
                                        SpaceH(AppSize.w10),
                                        Text(
                                          AppLocalizations.of(context)!.hours,
                                          style: context.textTheme.bodyMedium
                                              ?.copyWith(
                                            color:
                                                context.themeColors.textColor,
                                            fontSize: AppSize.sp20,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        )
                                      ],
                                    ),
                                    SpaceV(AppSize.h10),
                                    BalanceDataItem(
                                        title: "TVT", data: value.tvt ?? "-"),
                                    BalanceDataItem(
                                        title: "Sparen",
                                        data: value.sparen ?? "-"),
                                  ],
                                ),
                              ),
                              SpaceV(AppSize.h10),
                              Divider(
                                color: context.themeColors.greyColor,
                              ),
                              Container(
                                decoration: BoxDecoration(
                                  color: value.totaal != null &&
                                          value.totaal!.contains('-')
                                      ? Colors.red.shade50
                                      : Colors.green.shade100,
                                  border: Border.all(
                                      color: value.totaal != null &&
                                              value.totaal!.contains('-')
                                          ? Colors.red
                                          : Colors.green),
                                ),
                                padding: EdgeInsets.symmetric(
                                    horizontal: AppSize.w16,
                                    vertical: AppSize.h10),
                                margin: EdgeInsets.symmetric(
                                    vertical: AppSize.h10,
                                    horizontal: AppSize.w20),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Text(
                                      AppLocalizations.of(context)!.totalText,
                                      style: context.textTheme.bodyMedium
                                          ?.copyWith(
                                        color: context.themeColors.textColor,
                                        fontSize: AppSize.sp20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Text(
                                      value.totaal ?? '-',
                                      style: context.textTheme.bodyMedium
                                          ?.copyWith(
                                              color:
                                                  context.themeColors.textColor,
                                              fontSize: AppSize.sp26,
                                              fontWeight: FontWeight.bold),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              );
              // }
            },
          ),
        ],
      ),
    );
  }
}

class BalanceDataItem extends StatelessWidget {
  final String title;
  final String data;

  const BalanceDataItem({
    Key? key,
    required this.title,
    required this.data,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: AppSize.w12,
        vertical: AppSize.w12,
      ),
      child: Column(
        children: [
          Text(
            title,
            style: context.textTheme.bodyMedium?.copyWith(
              color: context.themeColors.textColor,
              fontSize: AppSize.sp13,
              fontWeight: FontWeight.normal,
            ),
          ),
          SizedBox(
            height: AppSize.h6,
          ),
          Text(
            data,
            style: context.textTheme.bodyMedium?.copyWith(
              color: context.themeColors.textColor,
              fontSize: AppSize.sp26,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

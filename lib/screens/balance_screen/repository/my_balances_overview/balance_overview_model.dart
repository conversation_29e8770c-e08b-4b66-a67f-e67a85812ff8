class MyBalancesOverviewModel {
  int? sequence;
  String? categorie;
  bool? total;
  String? vak;
  String? atv;
  String? tvt;
  String? sparen;
  String? totaal;

  MyBalancesOverviewModel({
     this.sequence,
     this.categorie,
     this.total,
     this.vak,
     this.atv,
    this.tvt,
    this.sparen,
     this.totaal,
  });

  factory MyBalancesOverviewModel.fromJson(Map<String, dynamic> json) => MyBalancesOverviewModel(
        sequence: json["Sequence"],
        categorie: json["Categorie"],
        total: json["Total"],
        vak: json["VAK"],
        atv: json["ATV"],
        tvt: json["TVT"],
        sparen: json["Sparen"],
        totaal: json["Totaal"],
      );

  Map<String, dynamic> toJson() => {
        "Sequence": sequence,
        "Categorie": categorie,
        "Total": total,
        "VAK": vak,
        "ATV": atv,
        "TVT": tvt,
        "Sparen": sparen,
        "Totaal": totaal,
      };
}

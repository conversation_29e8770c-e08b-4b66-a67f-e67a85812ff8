import 'package:flutter/material.dart';
import 'package:staff_medewerker/screens/balance_screen/repository/balance_api_provider.dart';
import 'package:staff_medewerker/screens/balance_screen/repository/my_balances_overview/balance_overview_model.dart';

class MyBalancesOverviewRepository {
  final balanceApiProvider = BalanceApiProvider();

  Future<List<MyBalancesOverviewModel>?> myBalancesOverviewApi({required BuildContext context, required String selectedDate}) async {
    final response = await balanceApiProvider.myBalancesOverviewApiCall(context,selectedDate);

    if (response != null && response.data is List<dynamic>) {
      List<dynamic> dataList = response.data;
      List<MyBalancesOverviewModel> myBalancesOverview = [];

      for (var item in dataList) {
        myBalancesOverview.add(MyBalancesOverviewModel.fromJson(item));
      }

      return myBalancesOverview;
    } else {
      return null;
    }
  }
}
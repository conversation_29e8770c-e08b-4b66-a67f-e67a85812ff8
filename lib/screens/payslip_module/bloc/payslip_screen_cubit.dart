import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:staff_medewerker/common/custom_widgets/common_snackbar.dart';
import 'package:staff_medewerker/main.dart';
import 'package:url_launcher/url_launcher.dart';

import '../repository/model.dart';
import '../repository/payslip_api_repository.dart';

class PaySlipCubit extends Cubit<bool> {
  PaySlipCubit() : super(false);
  ValueNotifier<bool> isLoading = ValueNotifier(true);
  ValueNotifier<bool> isDocumentLoading = ValueNotifier(false);
  ValueNotifier<bool> isClockingIn = ValueNotifier(false);
  List<PaySlipModel> paySlipList = [];
  final PaySlipApiRepository paySlipApiRepository = PaySlipApiRepository();

  Future<void> paySlipApiCall({required BuildContext context}) async {
    paySlipList.clear();
    isLoading.value = true;
    //print("api started =====>");
    final response = await paySlipApiRepository.paySlipApi(context: context);
    print("api done =====>${response}");
    paySlipList = response!;
   // print("api started =====>123${paySlipList[0].fileId}");

    isLoading.value = false;
  }

  Future<void> paySlipDocumentData(
      {required BuildContext context, required String guid}) async {
    isDocumentLoading.value = true;
    customSnackBar(snackBarDuration: 5000,
        context: navigatorKey.currentContext!,
        message: AppLocalizations.of(context)!.openFile,
        actionButtonText:
        AppLocalizations.of(context)!.closeText.toUpperCase());
    print("api started =====>");
    final response = await paySlipApiRepository.paySlipDocumentApi(
        context: context, guid: guid);

    isDocumentLoading.value = false;
    String? path;
    if (Platform.isAndroid) {
      await getTemporaryDirectory().then((value) => path = value.path);
    } else if (Platform.isIOS) {
      await getTemporaryDirectory().then((value) => path = value.path);
    }

    Future launchURL(String url) async {
      try {
        log("URL : $url");
        await launchUrl(Uri.parse(url));
      } catch (e) {
        try {
          await launchUrl(Uri.parse(url));
        } catch (e) {
          throw 'Could not launch $url, error: ${e.toString()}';
        }
      }
    }

    if (response?.data['FileContent'] != null) {
      final encodedStr = response?.data['FileContent'];
      Uint8List bytes = base64.decode(encodedStr);
      String filePath = '$path/${response?.data['FileName']}';
      File file = File(filePath);
      await file.writeAsBytes(bytes);
      log("api done =====>${response?.data['FileName']}");

      await OpenFile.open(file.path);
      ScaffoldMessenger.of(navigatorKey.currentContext!).hideCurrentSnackBar();

    } else if (response?.data['CloudURL'] != null) {

      await launchURL(response?.data['CloudURL']);
      ScaffoldMessenger.of(navigatorKey.currentContext!).hideCurrentSnackBar();

    } else {}
  }
}

import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/service/api_service/api_function.dart';
import 'package:staff_medewerker/service/api_service/server_constants.dart';

class TimeSheetApiProvider {
  Future<Response?> myTimeSheetApiCall(BuildContext context, String isoStartDate) async {
    try {
      final query = {
        "APIKeyLogin": {
          "DeviceId": deviceId,
          "APIKey": APIKey,
        },
        "ISOStartDate": isoStartDate,
        "ISOEndDate": isoStartDate
      };

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.myTimeSheetList,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> activityListApiCall(BuildContext context, {required String selectedDate}) async {
    try {
      final query = {
        "APIKeyLogin": {
          "DeviceId": deviceId,
          "APIKey": APIKey,
        },
        "ISOStartDate": selectedDate,
        "ISOEndDate": selectedDate
      };

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.activityList,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> departmentListApiCall(BuildContext context) async {
    try {
      final query = {
        "APIKeyLogin": {
          "DeviceId": deviceId,
          "APIKey": APIKey,
        }
      };

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.departmentListData,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> taskListApiCall(BuildContext context, String startIsoDate, String endIsoDate) async {
    try {
      final query = {
        "APIKeyLogin": {
          "DeviceId": deviceId,
          "APIKey": APIKey,
        },
        "ISOStartDate": startIsoDate,
        "ISOEndDate": endIsoDate
      };

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.taskList,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> timeSheetEmptyRowApiCall(BuildContext context, String iSODate) async {
    try {
      final query = {
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
        "ISODate": iSODate
      };

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.timeSheetEmptyRow,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> getDepartmentSettingApiCall(BuildContext context, {required String guid}) async {
    try {
      final query = {
        // "guid": "d9643fca-4e34-432f-bc4d-80f0ba291da8",
        "guid": guid,
        "APIKeyLogin": {"DeviceId": deviceId, "APIKey": APIKey},
      };

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.departmentSetting,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> costCentersNonTimeBoundPersonModelApiCall(
      BuildContext context, String startIsoDate, String endIsoDate) async {
    try {
      final query = {
        "APIKeyLogin": {
          "DeviceId": deviceId,
          "APIKey": APIKey,
        },
        "ISOStartDate": startIsoDate,
        "ISOEndDate": endIsoDate
      };

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.costCentersNonTimeBoundPerson,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> timeSheetDataSaveApiCall(BuildContext context, String myTimeSheetListJson) async {
    print("myTimeSheetListJson:::$myTimeSheetListJson");
    print("myTimeSheetListJson22:::${json.decode(myTimeSheetListJson)}");

    try {
      final query = {
        "APIKeyLogin": {
          "DeviceId": deviceId,
          "APIKey": APIKey,
        },
        "TimesheetTable": json.decode(myTimeSheetListJson),
      };

      log('=====>${query.toString()}');

      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.myTimeSheetDataSave,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }

  Future<Response?> activityExclusionsListApiCall(BuildContext context) async {
    try {
      final query = {
        "APIKeyLogin": {
          "DeviceId": deviceId,
          "APIKey": APIKey,
        }
      };
  
      log(query.toString());
  
      Response response = await APIFunction.postAPICall(
        apiName: ServerConstant.base_url + ServerConstant.costCentersExclusions,
        header: {
          ServerConstant.Header_Content_Key: ServerConstant.Header_Content_value,
          ServerConstant.Header_Accept_Key: ServerConstant.Header_Content_value
        },
        query,
        context: context,
      );
      return response;
    } catch (error, stacktrace) {
      print("Exception occurred: $error stackTrace: $stacktrace");
    }
    return null;
  }
}

// {
// "APIKeyLogin": {
// "DeviceId": "SE1A.211212.001.B1",
// "APIKey": "690eb680-eb9a-46ba-b6a6-704c695ee279"
// },
// "TimesheetTable": [
// {
// "guid": "927794f6-316d-4883-810b-b1a7fb4c0c66",
// "PersonId": "75637aff-4d38-40fe-a411-5dd100b965ac",
// "FullName": "StuartvanHeeswijk",
// "BirthDate": "1961-04-07T00:00:00",
// "Age": 62,
// "DailyHours": "07:36",
// "DailyHoursSource": (r),
// "VacationHours": "05:56",
// "ScheduledHours": null,
// "PersonRosterStartTime": "09:00",
// "CAOId": "bc076397-9e4e-42c1-900c-b8f7b69e50b7",
// "Sequence": 0,
// "PersonalBreakTime": null,
// "Date": "2023-09-28T00:00:00",
// "ISODate": "********",
// "Holiday": null,
// "DepartmentId": "c17238ad-5164-46e0-b443-6c921dd7746d",
// "HoursSource": null,
// "Remark": null,
// "TimeFrom": "11:00",
// "TimeUntil": "13:00",
// "BreakTime": "00:00",
// "CalendarEntryId": null,
// "AccountEntryId1": "971a92bc-8feb-4cc0-bb31-699f9c353119",
// "AccountEntryId2": null,
// "AccountEntryId3": null,
// "AccountEntryOrElseCostCenterId1": "0bf96fdf-19f4-4159-8f2c-d38b182af7af",
// "AccountEntryOrElseCostCenterId2": "b2979e9e-0208-4cdf-9510-555f7b59581b",
// "AccountEntryOrElseCostCenterId3": "0bbce6ce-18c8-4ed4-b45b-a313bdb096c7",
// "AccountEntryOrElseCostCenterId4": "6f37a572-85a5-437a-af1b-40567d179324",
// "tb1CostCenterId": "9978f115-bc62-478d-877b-8162a6d5e237",
// "tb2CostCenterId": null,
// "tb3CostCenterId": null,
// "tb1Hours": "02:00",
// "tb2Hours": null,
// 'tb3Hours': null,
// "tb1Remark": null,
// "tb2Remark": null,
// "tb3Remark": null,
// "ntb1Checked": true,
// "ntb2Checked": true,
// "ntb3Checked": false,
// "ntb4Checked": false,
// "IsSick": null,
// "IsLeave": true,
// "Temporary": false,
// "Approved": false,
// "EditRight": true,
// "Command": null
// },
// {
// "guid": null,
// "PersonId": "75637aff-4d38-40fe-a411-5dd100b965ac",
// "FullName": "StuartvanHeeswijk",
// "BirthDate": "1961-04-07T00:00:00",
// "Age": 62,
// "DailyHours": "07:36",
// "DailyHoursSource": "(r)",
// "VacationHours": "05:56",
// "ScheduledHours": null,
// "PersonRosterStartTime": "09:00",
// "CAOId": "bc076397-9e4e-42c1-900c-b8f7b69e50b7",
// "Sequence": 0,
// "PersonalBreakTime": null,
// "Date": "2023-09-28T00:00:00",
// "ISODate": "********",
// "Holiday": null,
// "DepartmentId": "c17238ad-5164-46e0-b443-6c921dd7746d",
// "HoursSource": null,
// "Remark": null,
// "TimeFrom": "18:30",
// "TimeUntil": "07:35",
// "BreakTime": "00:25",
// "CalendarEntryId": null,
// "AccountEntryId1": null,
// "AccountEntryId2": null,
// "AccountEntryId3": null,
// "AccountEntryOrElseCostCenterId1": "13e179cb-d062-46e2-8a69-1f7aebf946e4",
// "AccountEntryOrElseCostCenterId2": "ea750ee4-1467-427f-aa24-e01302ba40e6",
// "AccountEntryOrElseCostCenterId3": "0bbce6ce-18c8-4ed4-b45b-a313bdb096c7",
// "AccountEntryOrElseCostCenterId4": "6f37a572-85a5-437a-af1b-40567d179324",
// "tb1CostCenterId": "ba35f6b6-4358-4ac1-9fe6-b9122da6259f",
// "tb2CostCenterId": null,
// "tb3CostCenterId": null,
// "tb1Hours": null,
// "tb2Hours": null,
// "tb3Hours": null,
// "tb1Remark": null,
// "tb2Remark": null,
// "tb3Remark": null,
// "ntb1Checked": true,
// "ntb2Checked": false,
// "ntb3Checked": false,
// "ntb4Checked": false,
// "IsSick": null,
// "IsLeave": 1,
// "Temporary": 0,
// "Approved": 0,
// "EditRight": true,
// "Command": null
// }
// ]
// }

import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/activitylist_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/timesheet_api_provider.dart';

class ActivityListApiRepository {
  final timeSheetApiProvider = TimeSheetApiProvider();

  Future<List<ActivityListResponseModel>?> activityListApi({
    required BuildContext context,
    required String selectedDate,
  }) async {
    final response = await timeSheetApiProvider.activityListApiCall(
      context,
      selectedDate: selectedDate,
    );

    if (response != null && response.data is List<dynamic>) {
      List<dynamic> dataList = response.data;
      List<ActivityListResponseModel> activityList = [];

      for (var item in dataList) {
        activityList.add(ActivityListResponseModel.fromJson(item));
      }

      return activityList;
    } else {
      return null;
    }
  }
}

// class ActivityListApiRepository {
//   final timeSheetApiProvider = TimeSheetApiProvider();
//
//   Future<List<ActivityListResponseModel>?> activityListApi({
//     required BuildContext context,
//     required String iSOStartDate,
//     required String iSOEndDate,
//   }) async {
//     final response = await timeSheetApiProvider.activityListApiCall(
//       context,
//       iSOStartDate: iSOStartDate,
//       iSOEndDate: iSOEndDate,
//     );
//
//     if (response != null && response.data is List<dynamic>) {
//       List<dynamic> dataList = response.data;
//       List<ActivityListResponseModel> activityList = [];
//
//       for (var item in dataList) {
//         activityList.add(ActivityListResponseModel.fromJson(item));
//       }
//       List<ActivityListResponseModel> finalActivityList = [];
//
//       final clockingBloc = BlocProvider.of<ActivityCubit>(context);
//       clockingBloc.activityExclusionsList.forEach((element1) {
//         for (var element2 in activityList) {
//           if (element1.costCenterId != element2.costCenterId) {
//             finalActivityList.add(element2);
//           }
//         }
//       });
//
//       print("finalActivityList: $finalActivityList");
//       return activityList;
//     } else {
//       return null;
//     }
//   }
// }

import 'package:bloc/bloc.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/activity_exclusions_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/activitylist_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/activity_exclusions_list.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/activity_list_repository.dart';

part 'activity_state.dart';

class ActivityCubit extends Cubit<ActivityState> {
  ActivityCubit() : super(ActivityInitial());

  List<ActivityListResponseModel> activityList = [];
  String selectedActivityName = '';
  String selectedActivityId = '';
  List<ActivityListResponseModel> filterActivityList = [];
  List<ActivityListResponseModel> timeBondFalseList = [];
  List<ActivityExclusionsListResponseModel> activityExclusionsList = [];

  void fetchData({required String selectedValue, required String selectedId}) {
    filterActivityList = List.from(activityList);

    selectedActivityName = selectedValue;
    selectedActivityId = selectedId;
  }

  void filterActivity(String query) {
    filterActivityList.clear();
    filterActivityList =
        activityList.where((element) => element.title.toLowerCase().contains(query.toLowerCase())).toList();
    emit(ActivityInitial());

    print("filterValue=====>$filterActivityList");
  }

  Future<void> costCentersActivityApiCall({required BuildContext context, required String selectedDate}) async {
    print("api started =====>");
    final ActivityListApiRepository activityListApiRepository = ActivityListApiRepository();
    final response = await activityListApiRepository.activityListApi(context: context, selectedDate: selectedDate);
    print("api done =====>${response}");

    if (response!.isNotEmpty) {
      timeBondFalseList.clear();
      activityList.clear();

      List<ActivityListResponseModel> unFilteredActivityList = response;
      unFilteredActivityList.forEach((element) {
        if (element.valid == true) {
          if (element.timeBound == false) {
            timeBondFalseList.add(element);
          }
          if (element.productive == true && element.timeBound == true) {
            activityList.add(element);
          }
        }
      });

      DateTime selectedDate2 =
          DateTime.parse("${selectedDate.substring(0, 4)}${selectedDate.substring(4, 6)}${selectedDate.substring(6)}");

      unFilteredActivityList.forEach((element) {
        if (element.valid == true) {
          // selectedDate2 date must between element.startDate and element.endDate
          if ((element.productive == false && element.timeBound == true) &&
              (element.startDate.isBefore(selectedDate2) ||
                  (element.startDate.isAtSameMomentAs(selectedDate2) && element.endDate.isAfter(selectedDate2)) ||
                  element.endDate.isAtSameMomentAs(selectedDate2))) {
            activityList.add(element);
          }
        }
      });

      print("timeBondFalseList ${timeBondFalseList.length}");
      print("timeBondFalseList ${activityList.length}");
    } else {
      // customSnackBar(context: context, message: AppLocalizations.of(context)!.errorText,actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
    }
    emit(ActivityInitial());
  }

  Future<void> activityExclusionsApiCall({required BuildContext context}) async {
    print("api started =====>");
    final ActivityExclusionsApiRepository activityExclusionsApiRepository = ActivityExclusionsApiRepository();
    final response = await activityExclusionsApiRepository.activityExclusionsListApi(context: context);
    print("api done =====>${response}");
  
    if (response!.isNotEmpty) {
      activityExclusionsList.clear();
      activityExclusionsList.addAll(response);
    } else {
      // customSnackBar(context: context, message: AppLocalizations.of(context)!.errorText,actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
    }
    emit(ActivityInitial());
  }
}

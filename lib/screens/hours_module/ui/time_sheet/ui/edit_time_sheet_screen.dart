import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/common_functions/common_dateformat_function.dart';
import 'package:staff_medewerker/common/custom_date_picker/date_picker.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/common_button.dart';
import 'package:staff_medewerker/common/custom_widgets/common_snackbar.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_loader.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/bloc/time_sheet_cubit.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/my_timesheet_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/ui/activity_screen/bloc/activity_cubit.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/ui/add_time_sheet_screen.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/ui/department_screen/bloc/department_cubit.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/ui/task_screen/bloc/task_cubit.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';
import 'package:staff_medewerker/utils/date_language_convter.dart';

class EditTimeSheetScreen extends StatefulWidget {
  final String dayTitle;
  final String selectedYear;
  final bool isFromFloatingButton;
  final bool isFromEdit;

  EditTimeSheetScreen(
      {Key? key,
      required this.dayTitle,
      required this.selectedYear,
      this.isFromFloatingButton = false,
      this.isFromEdit = false})
      : super(key: key);

  @override
  State<EditTimeSheetScreen> createState() => _EditTimeSheetScreenState();
}

class _EditTimeSheetScreenState extends State<EditTimeSheetScreen> {
  String selectedDateFromDatePicker = "";

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    final timeBloc = context.read<TimeSheetCubit>();
    // Monday 23 September
    print("dayTitle isoDate: ${widget.dayTitle}");
    // String isoDate = timeBloc.extractDateFromTitle(widget.dayTitle);
    String isoDate = timeBloc.extractDateFromTitle(widget.dayTitle);
    //0923
    print("isoDate: $isoDate");
    timeBloc.selectedDateString.value =
        "${widget.dayTitle} ${widget.selectedYear}";
    print("isoDate: ${timeBloc.selectedDateString.value}");

    String iSODate = "${widget.selectedYear}$isoDate";
    selectedDateFromDatePicker = iSODate;

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      timeBloc.taskList.clear();
      timeBloc.myTimeSheetList.value.clear();
      context.read<TaskCubit>().taskList.clear();
      context.read<DepartmentCubit>().departmentList.clear();
      context.read<ActivityCubit>().activityList.clear();
      timeBloc.clearAllData(true);
      timeBloc.timeSheetApiCall(
          context: context, isoStartDate: "${widget.selectedYear}$isoDate");

      context
          .read<ActivityCubit>()
          .costCentersActivityApiCall(context: context, selectedDate: iSODate);

      context.read<DepartmentCubit>().departmentApiCall(context: context);
      context.read<TaskCubit>().taskApiCall(
          context: context, startIsoDate: iSODate, endIsoDate: iSODate);

      timeBloc.costCentersNonTimeBoundPersonApi(
          context: context, startIsoDate: iSODate, endIsoDate: iSODate);

      print("widget.dayTitle} ${widget.dayTitle} ${widget.selectedYear}");
    });
  }

  @override
  Widget build(BuildContext context) {
    context.read<DepartmentCubit>().departmentList.forEach((element) {
      print("element.title: ${element.title}");
    });
    final timeBloc = context.read<TimeSheetCubit>();
    String titleForNextPage = widget.dayTitle;

    return Scaffold(
        backgroundColor: context.themeColors.homeContainerColor,
        appBar: CustomAppBar(
          actions: true,
          isLeading: true,
          title: "",
          leadingWidth: AppSize.w250 + 10,
          leading: Padding(
            padding: EdgeInsets.only(left: AppSize.w12),
            child: BlocBuilder<TimeSheetCubit, TimeSheetState>(
              builder: (ctx, state) {
                return ValueListenableBuilder(
                  valueListenable: timeBloc.selectedDateString,
                  builder: (BuildContext context, value, Widget? child) {
                    // DateTime date = DateFormat("EEEE dd MMMM yyyy").parse(value);
                    // // Format the date without the year
                    // DateFormat formatter = DateFormat("EEEE dd MMMM");
                    // value = formatter.format(date);
                    titleForNextPage = value;
                    return InkWell(
                      onTap: () async {
                        DateTime initialDate2 = timeBloc
                            .stringToDate(timeBloc.selectedDateString.value);
                        print("initialDate: $initialDate2");

                        DateTime? selectedDate2 = await CustomDatePicker(
                          context: context,
                          initialDate: initialDate2,
                          firstDate: DateTime(1950),
                          lastDate: DateTime.now(),
                          initialEntryMode: DatePickerEntryMode.calendarOnly,
                          confirmText:
                              AppLocalizations.of(context)!.oK.toUpperCase(),
                          cancelText: AppLocalizations.of(context)!
                              .closeText
                              .toUpperCase(),
                          builder: (context, child) {
                            return Theme(
                              data: ThemeData.light().copyWith(
                                  colorScheme: ColorScheme.dark(
                                      onPrimary: AppColors
                                          .white, // selected text color
                                      onSurface: context.themeColors
                                          .textColor, // default date text color
                                      primary: AppColors
                                          .primaryColor, // circle color
                                      surface: context.themeColors
                                          .drawerColor // background color,
                                      ),
                                  dialogBackgroundColor:
                                      context.themeColors.listGridColor1,
                                  textButtonTheme: TextButtonThemeData(
                                      style: TextButton.styleFrom(
                                          foregroundColor:
                                              AppColors.primaryColor))),
                              child: child!,
                            );
                          },
                        );

                        if (selectedDate2 != null) {
                          // A date was selected by the user
                          if (selectedDate2 != initialDate2) {
                            selectedDateFromDatePicker =
                                DateFormatFunctions.formatDate(selectedDate2);
                            ctx
                                .read<TimeSheetCubit>()
                                .setDate(selectedValue: selectedDate2);
                            log("Selected date is: $selectedDate2");

                            //         selectedDateFromDatePicker = DateFormat('yyyyMMdd').format(selectedValue);
                            //         print("formattedDate$selectedDateFromDatePicker"); // Output: 20000118
                            //

                            timeBloc.taskList.clear();
                            context.read<TaskCubit>().taskList.clear();
                            context
                                .read<DepartmentCubit>()
                                .departmentList
                                .clear();
                            context.read<ActivityCubit>().activityList.clear();
                            timeBloc.clearAllData(true);

                            if (mounted) {
                              timeBloc.myTimeSheetList.value.clear();

                              timeBloc.timeSheetApiCall(
                                  context: context,
                                  isoStartDate: selectedDateFromDatePicker);

                              context
                                  .read<ActivityCubit>()
                                  .costCentersActivityApiCall(
                                      context: context,
                                      selectedDate: selectedDateFromDatePicker);

                              await context
                                  .read<DepartmentCubit>()
                                  .departmentApiCall(context: context);
                              await context.read<TaskCubit>().taskApiCall(
                                  context: context,
                                  startIsoDate: selectedDateFromDatePicker,
                                  endIsoDate: selectedDateFromDatePicker);

                              await timeBloc.costCentersNonTimeBoundPersonApi(
                                  context: context,
                                  startIsoDate: selectedDateFromDatePicker,
                                  endIsoDate: selectedDateFromDatePicker);
                            }
                          } else {
                            print(
                                "User selected the same date as the initial date.");
                          }
                        } else {
                          print("No date was selected by the user.");
                        }

                        // showModalBottomSheet(
                        //   context: context,
                        //   builder: (BuildContext context1) {
                        //     return TimeSheetDatePicker(
                        //       onOkPressed: (DateTime selectedValue) async {
                        //         print(selectedValue);
                        //         ctx.read<TimeSheetCubit>().setDate(selectedValue: selectedValue);
                        //         log("Selected date is: $selectedValue");
                        //
                        //         // Format the selectedValue to "yyyyMMdd" format
                        //         selectedDateFromDatePicker = DateFormat('yyyyMMdd').format(selectedValue);
                        //         print("formattedDate$selectedDateFromDatePicker"); // Output: 20000118
                        //         final timeBloc = context.read<TimeSheetCubit>();
                        //
                        //         timeBloc.taskList.clear();
                        //         timeBloc.departmentList.clear();
                        //         timeBloc.activityList.clear();
                        //         context.read<TaskCubit>().taskList.clear();
                        //         context.read<DepartmentCubit>().departmentList.clear();
                        //         context.read<ActivityCubit>().activityList.clear();
                        //         timeBloc.clearAllData();
                        //
                        //         if (mounted) {
                        //           timeBloc.myTimeSheetList.value.clear();
                        //
                        //           timeBloc.timeSheetApiCall(context: context, isoStartDate: selectedDateFromDatePicker);
                        //
                        //           context.read<ActivityCubit>().costCentersActivityApiCall(
                        //               context: context,
                        //               iSOStartDate: selectedDateFromDatePicker,
                        //               iSOEndDate: selectedDateFromDatePicker);
                        //
                        //           await context.read<DepartmentCubit>().departmentApiCall(context: context);
                        //           await context.read<TaskCubit>().taskApiCall(
                        //               context: context,
                        //               startIsoDate: selectedDateFromDatePicker,
                        //               endIsoDate: selectedDateFromDatePicker);
                        //
                        //           await timeBloc.costCentersNonTimeBoundPersonApi(
                        //               context: context,
                        //               startIsoDate: selectedDateFromDatePicker,
                        //               endIsoDate: selectedDateFromDatePicker);
                        //         }
                        //       },
                        //     );
                        //   },
                        // );
                      },
                      child: Row(
                        children: [
                          GestureDetector(
                            onTap: () {
                              AppNavigation.previousScreen(context);
                            },
                            child: Icon(
                              Ionicons.arrow_back_outline,
                            ),
                          ),
                          Expanded(
                            child: Text(
                              DateLanguageConverter.convertEnglishToDutch(
                                  value),
                              textAlign: TextAlign.center,
                              style: context.textTheme.headlineLarge!.copyWith(
                                color: Colors.white,
                                fontSize: AppSize.sp17,
                                fontWeight: FontWeight.w500,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Icon(Icons.arrow_drop_down_rounded,
                              size: AppSize.sp30),
                        ],
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ),
        body: BlocBuilder<TimeSheetCubit, TimeSheetState>(
          builder: (context, state) {
            final timeBloc = context.read<TimeSheetCubit>();

            return ValueListenableBuilder(
                valueListenable: timeBloc.isLoading,
                builder:
                    (BuildContext context1, bool isLoading, Widget? child) {
                  if (isLoading) {
                    return Center(
                      child: Image.asset(
                        'assets/gif/loader_gif.gif',
                        height: AppSize.h60,
                        width: AppSize.w60,
                        color: context.themeColors.textColor,
                      ),
                    );
                  } else {
                    return ValueListenableBuilder(
                      valueListenable: timeBloc.myTimeSheetList,
                      builder: (BuildContext context2,
                          List<MyTimeSheetResponseModel> timeSheetList,
                          Widget? child) {
                        return Stack(
                          clipBehavior: Clip.none,
                          children: [
                            SingleChildScrollView(
                              child: Column(
                                children: [
                                  Container(
                                    height: AppSize.h42,
                                    color: context.themeColors.listGridColor1,
                                    padding: EdgeInsets.symmetric(
                                        horizontal: AppSize.w14),
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                        AppLocalizations.of(context)!.timeSheet,
                                        style: context.textTheme.bodyMedium
                                            ?.copyWith(
                                          color: context.themeColors.textColor,
                                          fontSize: AppSize.sp15,
                                        )),
                                  ),
                                  timeSheetList.length == 0
                                      ? Container(
                                          alignment: Alignment.centerLeft,
                                          color: context
                                              .themeColors.homeContainerColor,
                                          child: Padding(
                                            padding: EdgeInsets.symmetric(
                                              horizontal: AppSize.w12,
                                              vertical: AppSize.h12,
                                            ),
                                            child: Text(
                                              AppLocalizations.of(context)!
                                                  .noTimeSheet,
                                              style: context
                                                  .textTheme.bodyMedium
                                                  ?.copyWith(
                                                color: context
                                                    .themeColors.textColor,
                                                fontSize: AppSize.sp15,
                                              ),
                                            ),
                                          ),
                                        )
                                      : Column(
                                          children: List.generate(
                                              timeSheetList.length, (index) {
                                            return InkWell(
                                              onTap: () {
                                                navigateNextScreen(
                                                    timeSheetList,
                                                    index,
                                                    timeBloc,
                                                    context,
                                                    titleForNextPage,
                                                    isFromFloatingButton: widget
                                                        .isFromFloatingButton);
                                              },
                                              child: Container(
                                                color: timeSheetList[index]
                                                            .isError ??
                                                        false
                                                    ? context.themeColors
                                                        .buttonRedColor
                                                    : context.themeColors
                                                        .homeContainerColor,
                                                // color: context.themeColors.homeContainerColor,
                                                child: Column(
                                                  children: [
                                                    Padding(
                                                      padding:
                                                          EdgeInsets.symmetric(
                                                        horizontal: AppSize.w14,
                                                      ),
                                                      child: Row(
                                                        children: [
                                                          timeBloc
                                                                      .myTimeSheetList
                                                                      .value[
                                                                          index]
                                                                      .editRight ==
                                                                  true
                                                              ? Container()
                                                              : Icon(
                                                                  Ionicons
                                                                      .lock_closed,
                                                                  size: AppSize
                                                                      .sp20,
                                                                  color: context
                                                                      .themeColors
                                                                      .darkGreyColor),
                                                          Text(
                                                            '${timeSheetList[index].timeFrom ?? ""} - ${timeSheetList[index].timeUntil ?? "00:00"}${timeBloc.newItemStartList[index]}',
                                                            // '12:00 - 10:00',
                                                            style: context
                                                                .textTheme
                                                                .bodyMedium
                                                                ?.copyWith(
                                                              color: context
                                                                  .themeColors
                                                                  .textColor,
                                                              fontSize:
                                                                  AppSize.sp15,
                                                            ),
                                                          ),
                                                          Spacer(),
                                                          IconButton(
                                                            onPressed: () {
                                                              navigateNextScreen(
                                                                  timeSheetList,
                                                                  index,
                                                                  timeBloc,
                                                                  context,
                                                                  titleForNextPage,
                                                                  isFromFloatingButton:
                                                                      widget
                                                                          .isFromFloatingButton);
                                                            },
                                                            icon: Icon(
                                                              Ionicons
                                                                  .chevron_forward_outline,
                                                              color: context
                                                                  .themeColors
                                                                  .greyColor,
                                                              size:
                                                                  AppSize.sp18,
                                                            ),
                                                          )
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            );
                                          }),
                                        ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Padding(
                                        padding:
                                            EdgeInsets.only(right: AppSize.w24),
                                        child: TextButton(
                                          onPressed: () async {
                                            await context
                                                .read<TimeSheetCubit>()
                                                .getTimeSheetEmptyRow(
                                                    context: context,
                                                    iSODate:
                                                        "${selectedDateFromDatePicker}")
                                                .then((value) async {
                                              await context
                                                  .read<TimeSheetCubit>()
                                                  .getDepartmentSetting(
                                                      context: context,
                                                      guid: timeBloc
                                                              .timeSheetEmptyRowList[
                                                                  0]
                                                              .departmentId ??
                                                          '');
                                              ;
                                            }).then((value) async {
                                              timeBloc.newItemStartList
                                                  .add(" *");
                                              timeBloc.myTimeSheetList.value
                                                  .addAll(timeBloc
                                                      .timeSheetEmptyRowList);
                                              print(
                                                  "myTimeSheetList.length: ${timeBloc.myTimeSheetList.value}");

                                              await AppNavigation.nextScreen(
                                                  context,
                                                  AddTimeSheetScreen(
                                                    isFromEdit: false,
                                                    index: timeBloc
                                                            .myTimeSheetList
                                                            .value
                                                            .length -
                                                        1,
                                                    appBarTitle:
                                                        titleForNextPage,
                                                    selectedDate:
                                                        "${widget.dayTitle}${widget.selectedYear}",
                                                  ));
                                              timeBloc.rebuildScreen();
                                            });
                                          },
                                          child: Text(
                                            AppLocalizations.of(context)!
                                                .addTimeSheet
                                                .toUpperCase(),
                                            style: context.textTheme.bodyMedium
                                                ?.copyWith(
                                                    color:
                                                        AppColors.primaryColor,
                                                    fontSize: AppSize.sp12,
                                                    fontWeight: FontWeight.w500,
                                                    letterSpacing: 0.8),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SpaceV(AppSize.h100),
                                ],
                              ),
                            ),
                            // ValueListenableBuilder(
                            //   valueListenable: timeBloc.isLoading,
                            //   builder: (BuildContext context, bool isLoading, Widget? child) {
                            //     // if (isLoading) {
                            //     // return Center(
                            //     //   child: Image.asset(
                            //     //     'assets/gif/loader_gif.gif',
                            //     //     height: AppSize.h60,
                            //     //     width: AppSize.w60,
                            //     //     color: AppColors.primaryColor,
                            //     //   ),
                            //     // );
                            //     // } else {
                            //     //   return
                            //     // }
                            //   },
                            // ),
                            timeSheetList.length == 0
                                ? Container()
                                : Align(
                                    alignment: Alignment.bottomCenter,
                                    child: Container(
                                      height: AppSize.h64,
                                      width: MediaQuery.of(context).size.width,
                                      color: context
                                          .themeColors.bottomBarBackgroundColor,
                                      child: Padding(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: AppSize.w14),
                                        child: SingleChildScrollView(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                AppLocalizations.of(context)!
                                                    .sheetNotSaved,
                                                textAlign: TextAlign.center,
                                                style: context
                                                    .textTheme.titleMedium
                                                    ?.copyWith(
                                                        fontSize: AppSize.sp15,
                                                        color: context
                                                            .themeColors
                                                            .darkGreyColor),
                                              ),
                                              SpaceV(AppSize.h8),
                                              ((timeSheetList[0].editRight ??
                                                          false) ||
                                                      timeSheetList.length != 0)
                                                  // ? CommonButton(
                                                  //     buttonColor: AppColors.primaryColor,
                                                  //     title: AppLocalizations.of(context)!.saveButtonText.toUpperCase(),
                                                  //     height: AppSize.h34,
                                                  //     borderRadius: AppSize.r6,
                                                  //     onPressed: () async {
                                                  //       bool isError = false;
                                                  //
                                                  //       for (int i = 0; i < timeBloc.myTimeSheetList.value.length; i++) {
                                                  //         final element = timeBloc.myTimeSheetList.value[i];
                                                  //         if (element.tb1Hours == "00:00") {
                                                  //           element.tb1Hours = null;
                                                  //         }
                                                  //         if (element.tb2Hours == "00:00") {
                                                  //           element.tb2Hours = null;
                                                  //         }
                                                  //         if (element.tb3Hours == "00:00") {
                                                  //           element.tb3Hours = null;
                                                  //         }
                                                  //
                                                  //         if (element.timeFrom == null || element.timeUntil == null) {
                                                  //           isError = true;
                                                  //           element.isError = true;
                                                  //           customSnackBar(
                                                  //               context: context,
                                                  //               message: AppLocalizations.of(context)!.timeNotSet);
                                                  //           break;
                                                  //         } else if ((element.tb1Hours != null &&
                                                  //                 element.tb1CostCenterId == null) ||
                                                  //             (element.tb2Hours != null &&
                                                  //                 element.tb2CostCenterId == null) ||
                                                  //             (element.tb3Hours != null &&
                                                  //                 element.tb3CostCenterId == null)) {
                                                  //           element.isError = true;
                                                  //           isError = true;
                                                  //           customSnackBar(
                                                  //               context: context,
                                                  //               message:
                                                  //                   AppLocalizations.of(context)!.noActivitySelectedText);
                                                  //           break;
                                                  //         } else if ((element.tb1RemarkRequired == true &&
                                                  //                 element.tb1Remark == "") ||
                                                  //             (element.tb2RemarkValidate == true &&
                                                  //                 element.tb2Remark == "") ||
                                                  //             (element.tb3RemarkValidate == true &&
                                                  //                 element.tb3Remark == "")) {
                                                  //           element.isError = true;
                                                  //           isError = true;
                                                  //           customSnackBar(
                                                  //               context: context,
                                                  //               message:
                                                  //                   AppLocalizations.of(context)!.passwordCantEmptyText);
                                                  //           break;
                                                  //         } else if (element.totalHours != element.totalActivityHours) {
                                                  //           element.isError = true;
                                                  //           isError = true;
                                                  //           customSnackBar(
                                                  //               context: context,
                                                  //               message:
                                                  //                   AppLocalizations.of(context)!.timeSheetFormError);
                                                  //           break;
                                                  //         }
                                                  //       }
                                                  //
                                                  //       if (!isError) {
                                                  //         for (int i = 0;
                                                  //             i < timeBloc.myTimeSheetList.value.length;
                                                  //             i++) {
                                                  //           if (i < timeBloc.newItemStartList.length &&
                                                  //               timeBloc.newItemStartList[i].contains(" *")) {
                                                  //             timeBloc.myTimeSheetList.value[i].command = "INSERT";
                                                  //           } else {
                                                  //             timeBloc.myTimeSheetList.value[i].command = "UPDATE";
                                                  //           }
                                                  //         }
                                                  //
                                                  //         String myTimeSheetListJson =
                                                  //             json.encode(timeBloc.myTimeSheetList.value);
                                                  //         log(myTimeSheetListJson);
                                                  //
                                                  //         List<dynamic> myTimeSheetList =
                                                  //             json.decode(myTimeSheetListJson);
                                                  //
                                                  //         myTimeSheetList = myTimeSheetList.map((item) {
                                                  //           if (item is Map<String, dynamic>) {
                                                  //             return Map<String, dynamic>.from(item)
                                                  //               ..removeWhere((key, value) => value == null);
                                                  //           }
                                                  //           return item;
                                                  //         }).toList();
                                                  //
                                                  //         myTimeSheetListJson = json.encode(myTimeSheetList);
                                                  //
                                                  //         log(myTimeSheetListJson);
                                                  //
                                                  //         Loader.showLoaderDialog(navigatorKey.currentContext!);
                                                  //         await timeBloc.myTimesheetDataSaveApi(
                                                  //             context: context, myTimeSheetListJson: myTimeSheetListJson);
                                                  //         Loader.closeLoadingDialog(navigatorKey.currentContext!);
                                                  //       }
                                                  //       timeBloc.rebuildScreen();
                                                  //     },
                                                  //   )
                                                  ? ReusableContainerButton(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              AppSize.sp4),
                                                      backgroundColor: AppColors
                                                          .primaryColor,
                                                      onPressed: () async {
                                                        bool isError = false;

                                                        for (int i = 0;
                                                            i <
                                                                timeBloc
                                                                    .myTimeSheetList
                                                                    .value
                                                                    .length;
                                                            i++) {
                                                          final element = timeBloc
                                                              .myTimeSheetList
                                                              .value[i];

                                                          log('tb1Hours ${element.tb1Hours}');
                                                          // log('tb1Hours ${element.tb1CostCenterId}');
                                                          log('tb1Hours ${element.tb2Hours}');
                                                          log('tb1Hours ${element.tb3Hours}');
                                                          // log('tb1Hours ${element.tb3CostCenterId}');
                                                          log('tb1Hours ${element.timeFrom}');
                                                          log('tb1Hours ${element.timeUntil}');
                                                          if (element
                                                                  .tb1Hours ==
                                                              "00:00") {
                                                            element.tb1Hours =
                                                                null;
                                                          }
                                                          if (element
                                                                  .tb2Hours ==
                                                              "00:00") {
                                                            element.tb2Hours =
                                                                null;
                                                          }
                                                          if (element
                                                                  .tb3Hours ==
                                                              "00:00") {
                                                            element.tb3Hours =
                                                                null;
                                                          }

                                                          if (element.timeFrom == null ||
                                                              element.timeUntil ==
                                                                  null ||
                                                              element.timeFrom ==
                                                                  "00:00" ||
                                                              element.timeUntil ==
                                                                  "00:00") {
                                                            log('tb1Hours =====');
                                                            isError = true;
                                                            element.isError =
                                                                true;
                                                            customSnackBar(
                                                                context:
                                                                    context,
                                                                message: AppLocalizations.of(
                                                                        context)!
                                                                    .timeNotSet);
                                                            break;
                                                          } else if ((element
                                                                          .tb1Hours !=
                                                                      null &&
                                                                  element.tb1CostCenterId ==
                                                                      null) ||
                                                              (element.tb2Hours !=
                                                                      null &&
                                                                  element.tb2CostCenterId ==
                                                                      null) ||
                                                              (element.tb3Hours !=
                                                                      null &&
                                                                  element.tb3CostCenterId ==
                                                                      null)) {
                                                            element.isError =
                                                                true;
                                                            isError = true;
                                                            customSnackBar(
                                                                context:
                                                                    context,
                                                                message: AppLocalizations.of(
                                                                        context)!
                                                                    .noActivitySelectedText);
                                                            break;
                                                          } else if ((element
                                                                          .tb1RemarkRequired ==
                                                                      true &&
                                                                  element.tb1Remark ==
                                                                      "") ||
                                                              (element.tb2RemarkValidate ==
                                                                      true &&
                                                                  element.tb2Remark ==
                                                                      "") ||
                                                              (element.tb3RemarkValidate ==
                                                                      true &&
                                                                  element.tb3Remark ==
                                                                      "")) {
                                                            element.isError =
                                                                true;
                                                            isError = true;
                                                            customSnackBar(
                                                                context:
                                                                    context,
                                                                message: AppLocalizations.of(
                                                                        context)!
                                                                    .passwordCantEmptyText);
                                                            break;
                                                          } else if (element
                                                                  .totalHours !=
                                                              element
                                                                  .totalActivityHours) {
                                                            element.isError =
                                                                true;
                                                            isError = true;
                                                            customSnackBar(
                                                                context:
                                                                    context,
                                                                message: AppLocalizations.of(
                                                                        context)!
                                                                    .timeSheetFormError);
                                                            break;
                                                          }
                                                        }

                                                        if (!isError) {
                                                          for (int i = 0;
                                                              i <
                                                                  timeBloc
                                                                      .myTimeSheetList
                                                                      .value
                                                                      .length;
                                                              i++) {
                                                            if (i <
                                                                    timeBloc
                                                                        .newItemStartList
                                                                        .length &&
                                                                timeBloc
                                                                    .newItemStartList[
                                                                        i]
                                                                    .contains(
                                                                        " *")) {
                                                              timeBloc
                                                                      .myTimeSheetList
                                                                      .value[i]
                                                                      .command =
                                                                  "INSERT";
                                                            } else {
                                                              timeBloc
                                                                      .myTimeSheetList
                                                                      .value[i]
                                                                      .command =
                                                                  "UPDATE";
                                                            }
                                                          }

                                                          String
                                                              myTimeSheetListJson =
                                                              json.encode(timeBloc
                                                                  .myTimeSheetList
                                                                  .value);
                                                          log(myTimeSheetListJson);

                                                          List<dynamic>
                                                              myTimeSheetList =
                                                              json.decode(
                                                                  myTimeSheetListJson);

                                                          myTimeSheetList =
                                                              myTimeSheetList
                                                                  .map((item) {
                                                            if (item is Map<
                                                                String,
                                                                dynamic>) {
                                                              return Map<String,
                                                                      dynamic>.from(
                                                                  item)
                                                                ..removeWhere(
                                                                    (key, value) =>
                                                                        value ==
                                                                        null);
                                                            }
                                                            return item;
                                                          }).toList();

                                                          myTimeSheetListJson =
                                                              json.encode(
                                                                  myTimeSheetList);

                                                          log(myTimeSheetListJson);

                                                          Loader.showLoaderDialog(
                                                              navigatorKey
                                                                  .currentContext!);
                                                          await timeBloc
                                                              .myTimesheetDataSaveApi(
                                                                  context:
                                                                      context,
                                                                  myTimeSheetListJson:
                                                                      myTimeSheetListJson);
                                                          Loader.closeLoadingDialog(
                                                              navigatorKey
                                                                  .currentContext!);
                                                        }
                                                        timeBloc
                                                            .rebuildScreen();
                                                      },
                                                      buttonText:
                                                          AppLocalizations.of(
                                                                  context)!
                                                              .saveButtonText
                                                              .toUpperCase(),
                                                      height: AppSize.h32,
                                                      textStyle: TextStyle(
                                                          fontSize:
                                                              AppSize.sp14,
                                                          color:
                                                              AppColors.white,
                                                          fontWeight:
                                                              FontWeight.w500),
                                                    )
                                                  : Container(),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  )
                          ],
                        );
                      },
                    );
                  }
                });
          },
        ));
  }

  void navigateNextScreen(
      List<MyTimeSheetResponseModel> timeSheetList,
      int index,
      TimeSheetCubit timeBloc,
      BuildContext context,
      String titleForNextPage,
      {required bool isFromFloatingButton}) {
    print("timeSheetList[index].timeFrom :${timeSheetList[index].timeFrom}");
    print("timeSheetList[index].timeFrom :$index");

    print(
        "breakTime timeBloc.myTimeSheetList.value[widget.index].tb1Remark${timeBloc.myTimeSheetList.value[index].breakTime}");

    context
        .read<TimeSheetCubit>()
        .getDepartmentSetting(
            context: context,
            guid: timeBloc.myTimeSheetList.value[index].departmentId ?? '')
        .then((value) async {
      await AppNavigation.nextScreen(
        context,
        AddTimeSheetScreen(
          isFromEdit: true,
          index: index,
          appBarTitle: titleForNextPage,
          selectedDate: "${widget.dayTitle}${widget.selectedYear}",
        ),
      );
      timeBloc.rebuildScreen();
    });
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/shimmer_effect.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/ui/department_screen/bloc/department_cubit.dart';
import 'package:staff_medewerker/screens/hours_module/widget/search_text_field.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

class DepartmentScreen extends StatelessWidget {
  final String selectedDepartmentValue;
  final String selectedDepartmentId;

  DepartmentScreen({Key? key, required this.selectedDepartmentValue, required this.selectedDepartmentId})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    context
        .read<DepartmentCubit>()
        .setDepartmentData(selectedValue: selectedDepartmentValue, selectedId: selectedDepartmentId, context: context);
    return Scaffold(
      backgroundColor: context.themeColors.homeContainerColor,
      appBar: CustomAppBar(
        actions: true,
        isLeading: true,
        leading: GestureDetector(
            onTap: () {
              AppNavigation.previousScreen(context);
            },
            child: Icon(Ionicons.close_outline)),
        title: AppLocalizations.of(context)!.departmentText,
      ),
      body: BlocBuilder<DepartmentCubit, DepartmentState>(
        builder: (ctx, state) {
          final departmentBloc = ctx.read<DepartmentCubit>();

          return Column(
            children: [
              HourSearchTextField(
                onChanged: (value) {
                  print("value ======>$value");
                  departmentBloc.filterDepartment(value);
                  print("filterDepartment: ${departmentBloc.filterDepartmentList}");
                },
              ),
              ValueListenableBuilder(
                valueListenable: departmentBloc.isLoading,
                builder: (BuildContext context, isLoading, Widget? child) {
                  return isLoading
                      ? Expanded(
                          child: ListView.builder(
                            itemCount: 15,
                            itemBuilder: (context, index) {
                              return ShimmerWidget(
                                margin: EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                                height: AppSize.h20,
                              );
                            },
                          ),
                        )
                      : Expanded(
                          child: ListView.builder(
                            itemCount: departmentBloc.filterDepartmentList.length,
                            shrinkWrap: true,
                            itemBuilder: (context, index) {
                              final departmentName = departmentBloc.filterDepartmentList[index];
                              print("departmentName: $departmentName");
                              return departmentBloc.filterDepartmentList[index].timesheetRegistration == false
                                  ? Container()
                                  : GestureDetector(
                                      onTap: () async {
                                        departmentBloc.selectedDepartmentName = departmentName.title.toString();
                                        departmentBloc.selectedDepartmentId = departmentName.guid.toString();
                                        print("value ======>${departmentBloc.selectedDepartmentName}");
                                        print("value ======>${departmentBloc.selectedDepartmentId}");

                                        var result = {
                                          "selectedDepartmentName": departmentBloc.selectedDepartmentName,
                                          "selectedDepartmentId": departmentBloc.selectedDepartmentId
                                        };

                                        Navigator.pop(context, result);
                                      },
                                      child: Padding(
                                        padding: EdgeInsets.only(left: AppSize.w16, top: AppSize.h14),
                                        child: Row(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Container(
                                              width: AppSize.w16,
                                              height: AppSize.w16,
                                              decoration: BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  border: Border.all(
                                                    color: departmentBloc.selectedDepartmentName == departmentName.title
                                                        ? AppColors.primaryColor
                                                        : Colors.grey,
                                                  ),
                                                  color: departmentBloc.selectedDepartmentName == departmentName.title
                                                      ? AppColors.primaryColor
                                                      : null),
                                              child: Center(
                                                child: departmentBloc.selectedDepartmentName == departmentName.title
                                                    ? Icon(
                                                        Icons.check,
                                                        size: AppSize.sp14,
                                                        color: Colors.white,
                                                      )
                                                    : null,
                                              ),
                                            ),
                                            SpaceH(AppSize.w20),
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    departmentName.title.toString(),
                                                    style: context.textTheme.bodyMedium?.copyWith(
                                                      color: context.themeColors.textColor,
                                                      fontSize: AppSize.sp15,
                                                    ),
                                                  ),
                                                  SpaceV(AppSize.h10),
                                                  Container(
                                                    color: context.themeColors.dividerAvailbilityColor,
                                                    width: double.infinity,
                                                    height: AppSize.h1,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    );
                            },
                          ),
                        );
                },
              ),
            ],
          );
        },
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';

import '../../../utils/appsize.dart';
import '../../../utils/colors/app_colors.dart';

class HourSearchTextField extends StatelessWidget {
  final void Function(String)? onChanged;

  const HourSearchTextField({Key? key, this.onChanged}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.zero),
      child: Container(
        margin: EdgeInsets.all(AppSize.sp6),
        padding: EdgeInsets.symmetric(horizontal:AppSize.sp10),
        height: AppSize.h36,
        decoration: BoxDecoration(
          color: context.themeColors.homeContainerColor,
          boxShadow: [
            BoxShadow(
              color: context.themeColors.homeShadowColor,
              blurRadius: 2,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: AppSize.h4, horizontal: AppSize.w4),
          child: TextFormField(
            decoration: InputDecoration(
                contentPadding: EdgeInsets.symmetric(horizontal: AppSize.w10, vertical: AppSize.h10),
                // fillColor: Colors.red,
                //   filled: true,

                hintText: 'Type to search',
                prefixIcon: Icon(
                  Icons.search,
                  color: AppColors.darkGreyColor,
                ),
                focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(0), borderSide: BorderSide.none),
                enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(0), borderSide: BorderSide.none)),
            onChanged: onChanged,
          ),
        ),
      ),
    );
  }
}

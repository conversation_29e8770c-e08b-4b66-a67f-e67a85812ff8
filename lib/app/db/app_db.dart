import 'package:hive/hive.dart';
import 'package:staff_medewerker/app/db/app_db_models/user_credential.dart';
import 'package:staff_medewerker/app/db/app_db_models/user_data.dart';

class AppDB {
  AppDB(this._box);

  static const fcmKey = 'fcm_key';
  static const platform = 'platform';
  static const _appDbBox = '_appDbBox';

  final Box<dynamic> _box;

  static Future<AppDB> getInstance() async {
    final box = await Hive.openBox<dynamic>(_appDbBox);
    return AppDB(box);
  }

  Future<void> clearData() async {
    await _box.clear();
  }

  T getValue<T>(String key, {T? defaultValue}) => _box.get(key, defaultValue: defaultValue) as T;

  Future<void> setValue<T>(String key, T value) => _box.put(key, value);

  //Setting variables
  set language(String update) => setValue('language', update);
  String get language => getValue('language', defaultValue: 'nl');

  set dashBoardMode(int count) => setValue('dash_mode', count);
  int get dashBoardMode => getValue('dash_mode', defaultValue: 2);

  set darkTheme(bool update) => setValue('darkTheme', update);
  bool get darkTheme => getValue('darkTheme', defaultValue: false);

  set shareAnyltical(bool update) => setValue('shareAnyltical', update);
  bool get shareAnyltical => getValue('shareAnyltical', defaultValue: false);

  set newsItem(int count) => setValue('new_item', count);
  int get newsItem => getValue('new_item', defaultValue: 5);

  set savedPinNumber(String update) => setValue('savedPinNumber', update);
  String get savedPinNumber => getValue('savedPinNumber', defaultValue: '');

  //models
  set user(UserData? user) => setValue('user', user);
  UserData? get user => getValue('user');

  set userCredential(UserCredentialModel userCredential) => setValue('userCredential', userCredential);
  UserCredentialModel get userCredential =>
      getValue('userCredential', defaultValue: UserCredentialModel(username: "", password: ""));
}

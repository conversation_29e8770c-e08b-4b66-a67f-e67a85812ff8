import 'dart:developer';

import 'package:animated_splash_screen/animated_splash_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:persistent_bottom_nav_bar/persistent_tab_view.dart';
import 'package:staff_medewerker/app/bloc/localization_cubit/language_cubit.dart';
import 'package:staff_medewerker/common/functions/shared_prefs.dart';
import 'package:staff_medewerker/screens/absence_module/bloc/absence_screen_cubit.dart';
import 'package:staff_medewerker/screens/balance_screen/bloc/balance_screen_cubit.dart';
import 'package:staff_medewerker/screens/absence_module/ui/request_leave_screen/bloc/request_leave_screen_cubit.dart';
import 'package:staff_medewerker/screens/absence_module/ui/widgets/custom_datepicker/datepicker_cubit.dart';
import 'package:staff_medewerker/screens/authentication_module/forget_password_module/bloc/forget_password_cubit.dart';
import 'package:staff_medewerker/screens/authentication_module/login_module/bloc/login_cubit.dart';
import 'package:staff_medewerker/screens/authentication_module/login_module/ui/login_screen.dart';
import 'package:staff_medewerker/screens/authentication_module/on_boarding_module/on_boarding_screen/bloc/scanner_cubit.dart';
import 'package:staff_medewerker/screens/authentication_module/on_boarding_module/welcome_screen.dart';
import 'package:staff_medewerker/screens/availability_module/bloc/availability_cubit.dart';
import 'package:staff_medewerker/screens/clocking_module/bloc/clocking_screen_cubit.dart';
import 'package:staff_medewerker/screens/clocking_module/ui/select_activity_screen/bloc/select_activity_cubit.dart';
import 'package:staff_medewerker/screens/clocking_module/ui/select_department_screen/bloc/select_department_cubit.dart';
import 'package:staff_medewerker/screens/declaration_module/cubit/declration_cubit.dart';
import 'package:staff_medewerker/screens/home_module/bloc/home_cubit.dart';
import 'package:staff_medewerker/screens/home_module/ui/bottom_bar_screen.dart';
import 'package:staff_medewerker/screens/hours_module/bloc/hours_cubit.dart';
import 'package:staff_medewerker/screens/hours_module/ui/hours_week/bloc/hours_week_cubit.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/ui/department_screen/bloc/department_cubit.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/ui/task_screen/bloc/task_cubit.dart';
import 'package:staff_medewerker/screens/news_module/bloc/news_screen_cubit.dart';
import 'package:staff_medewerker/screens/notification_module/bloc/notification_cubit.dart';
import 'package:staff_medewerker/screens/open_service_module/bloc/open_service_cubit.dart';
import 'package:staff_medewerker/screens/pin_module/bloc/pin_screen_cubit.dart';
import 'package:staff_medewerker/screens/pin_module/pin_screen.dart';
import 'package:staff_medewerker/screens/profile_module/bloc/profile_screen_cubit.dart';
import 'package:staff_medewerker/screens/schedule_module/bloc/schedule_cubit.dart';
import 'package:staff_medewerker/screens/schedule_module/bloc/schedule_date_picker_cubit.dart';
import 'package:staff_medewerker/service/api_service/server_constants.dart';
import 'package:staff_medewerker/service/get_service_token.dart';
import 'package:staff_medewerker/service/notification_service/notification_service.dart';
import 'package:staff_medewerker/utils/asset_path/assets_path.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';
import 'package:staff_medewerker/utils/constant/constant.dart';

import 'app/bloc/app_theme_bloc/app_theme_cubit.dart';
import 'app/db/app_db.dart';
import 'app/db/hive_helper/register_adapters.dart';
import 'common/custom_widgets/custom_theme/theme_dark.dart' as theme;
import 'common/custom_widgets/custom_theme/theme_light.dart';
import 'screens/hours_module/ui/time_sheet/bloc/time_sheet_cubit.dart';
import 'screens/hours_module/ui/time_sheet/ui/activity_screen/bloc/activity_cubit.dart';
import 'screens/pin_module/bloc/pin_screen_cubit.dart';

final navigatorKey = GlobalKey<NavigatorState>();
PersistentTabController persistentTabController =
    PersistentTabController(initialIndex: 0);
int newIndex = 0;
final prefs = Prefs();
late AppDB appDB;
String deviceId = '';

String APIKey = '';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]);
  await prefs.initSharedPrefs();
  // hive setup
  await Hive.initFlutter();
  registerAdapters();
  appDB = await AppDB.getInstance();

  // get device id and api key
  deviceId = await GetTokenService.getDeviceId();
  APIKey = await GetTokenService.getApiKey();
  log('apikey ========>$APIKey');
  log('apikey ========>$deviceId');
  NotificationServices notificationServices = NotificationServices();
  notificationServices.initialiseNotification();
  ServerConstant.base_url = prefs.getString('base_url') ?? '';
  log('apikey ========>${ServerConstant.base_url}');

  runApp(MultiBlocProvider(
    providers: [
      BlocProvider(
        create: (_) => AppThemeCubit(),
      ),
      BlocProvider(
        create: (context) => LanguageCubit(locale: Locale(appDB.language)),
      ),
      BlocProvider(create: (context) => ScannerCubit()),
      BlocProvider(
        create: (context) => LoginCubit(),
      ),
      BlocProvider(
        create: (context) => HoursCubit(),
      ),
      BlocProvider(
        create: (context) => ClockingCubit(),
      ),
      BlocProvider(
        create: (context) => NotificationCubit(),
      ),
      BlocProvider(
        create: (context) => ForgetPasswordCubit(),
      ),
      BlocProvider(
        create: (context) => PinSetCubit(),
      ),
      BlocProvider(
        create: (context) => NewsCubit(),
      ),
      BlocProvider(
        create: (context) => DeclrationCubit(),
      ),
      BlocProvider(
        create: (context) => HoursWeekCubit(),
      ),
      BlocProvider(
        create: (context) => TimeSheetCubit(),
      ),
      BlocProvider(
        create: (context) => SelectActivityCubit(),
      ),
      BlocProvider(
        create: (context) => DepartmentCubit(),
      ),
      BlocProvider(
        create: (context) => ActivityCubit(),
      ),
      BlocProvider(
        create: (context) => ScheduleCubit(),
      ),
      BlocProvider(
        create: (context) => HomeCubit(),
      ),
      BlocProvider(
        create: (context) => SelectDepartmentCubit(),
      ),
      BlocProvider(
        create: (context) => TaskCubit(),
      ),
      BlocProvider(
        create: (context) => AbsenceCubit(),
      ),
      BlocProvider(
        create: (context) => BalanceCubit(),
      ),
      BlocProvider(
        create: (context) => RequestLeaveCubit(),
      ),
      BlocProvider(
        create: (context) => DateCubit(),
      ),
      BlocProvider(
        create: (context) => ScheduleTimeCubit(),
      ),
      BlocProvider(
        create: (context) => AvailabilityCubit(),
      ),
      BlocProvider(
        create: (context) => ProfileCubit(),
      ),
      BlocProvider(
        create: (context) => OpenServiceCubit(),
      ),
    ],
    child: const MyApp(),
  ));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LanguageCubit, Locale>(
      builder: (context, state) {
        return ScreenUtilInit(
          designSize: const Size(360, 690),
          minTextAdapt: true,
          splitScreenMode: true,
          builder: (context, child) {
            return BlocBuilder<AppThemeCubit, bool>(
              builder: (context, isDarkModeOn) {
                return MaterialApp(
                  debugShowCheckedModeBanner: false,
                  title: 'Staff',
                  theme: lightTheme,
                  darkTheme: theme.darkTheme,
                  navigatorKey: navigatorKey,
                  themeMode: appDB.darkTheme ? ThemeMode.dark : ThemeMode.light,

                  // TODO(H): add comment if you want en language

                  supportedLocales: AppLocalizations.supportedLocales,
                  localizationsDelegates: const [
                    AppLocalizations.delegate,
                    GlobalMaterialLocalizations.delegate,
                    GlobalCupertinoLocalizations.delegate,
                    GlobalWidgetsLocalizations.delegate
                  ],
                  locale: state,
                  home: AnimatedSplashScreen(
                    // splash: Lottie.asset(
                    //   'assets/logos/Splash animation.json',
                    //   fit: BoxFit.cover,
                    // ),
                    splash: Image.asset(
                      AssetsPath.splashLogo,
                      fit: BoxFit.cover,
                    ),
                    splashIconSize: double.infinity,
                    backgroundColor: AppColors.white,
                    nextScreen: getScreen(),
                    splashTransition: SplashTransition.fadeTransition,
                    // splashTransition: SplashTransition.fadeTransition,
                  ),
                );
              },
            );
          },
        );
      },
    );
  }
}

Widget getScreen() {
  // String? lastPosition = AppConstants.main;
  String? lastPosition =
      prefs.getString(AppConstants.lastPosition) ?? AppConstants.welcomeScreen;
  //print("lastPosition =====>$lastPosition");
  switch (lastPosition) {
    case AppConstants.welcomeScreen:
      {
        return WelComeScreen();
      }
    case AppConstants.loginScreen:
      {
        return LoginScreen();
      }
    case AppConstants.homeScreen:
      {
        String savedPin = appDB.savedPinNumber;
        // String savedPin =  Prefs().getString(AppConstants.savedPinNumber) ?? "";
        if (savedPin.isEmpty) {
          return BottomBarScreen();
        } else {
          return EnterPinPage(
              isRemovePinScreen: false, isNavigateFromHomeScreen: true);
        }
      }
    default:
      return const WelComeScreen();
  }
}
